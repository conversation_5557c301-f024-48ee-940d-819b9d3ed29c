/* Generated by Cython 0.29.36 */

/* BEGIN: Cython Metadata
{
    "distutils": {
        "depends": [
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-a8vvuhsc\\overlay\\Lib\\site-packages\\numpy\\core\\include\\numpy\\arrayobject.h",
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-a8vvuhsc\\overlay\\Lib\\site-packages\\numpy\\core\\include\\numpy\\arrayscalars.h",
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-a8vvuhsc\\overlay\\Lib\\site-packages\\numpy\\core\\include\\numpy\\ndarrayobject.h",
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-a8vvuhsc\\overlay\\Lib\\site-packages\\numpy\\core\\include\\numpy\\ndarraytypes.h",
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-a8vvuhsc\\overlay\\Lib\\site-packages\\numpy\\core\\include\\numpy\\ufuncobject.h",
            "C:\\Users\\<USER>\\AppData\\Local\\pypa\\cibuildwheel\\Cache\\nuget-cpython\\python.3.10.11\\tools\\Include\\Python.h"
        ],
        "extra_compile_args": [
            "-std=c++11"
        ],
        "include_dirs": [
            "C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-build-env-a8vvuhsc\\overlay\\Lib\\site-packages\\numpy\\core\\include",
            "C:\\Users\\<USER>\\AppData\\Local\\pypa\\cibuildwheel\\Cache\\nuget-cpython\\python.3.10.11\\tools\\Include"
        ],
        "language": "c++",
        "name": "spacy.pipeline._parser_internals._state",
        "sources": [
            "spacy/pipeline/_parser_internals/_state.pyx"
        ]
    },
    "module_name": "spacy.pipeline._parser_internals._state"
}
END: Cython Metadata */

#ifndef PY_SSIZE_T_CLEAN
#define PY_SSIZE_T_CLEAN
#endif /* PY_SSIZE_T_CLEAN */
#include "Python.h"
#ifndef Py_PYTHON_H
    #error Python headers needed to compile C extensions, please install development version of Python.
#elif PY_VERSION_HEX < 0x02060000 || (0x03000000 <= PY_VERSION_HEX && PY_VERSION_HEX < 0x03030000)
    #error Cython requires Python 2.6+ or Python 3.3+.
#else
#define CYTHON_ABI "0_29_36"
#define CYTHON_HEX_VERSION 0x001D24F0
#define CYTHON_FUTURE_DIVISION 0
#include <stddef.h>
#ifndef offsetof
  #define offsetof(type, member) ( (size_t) & ((type*)0) -> member )
#endif
#if !defined(WIN32) && !defined(MS_WINDOWS)
  #ifndef __stdcall
    #define __stdcall
  #endif
  #ifndef __cdecl
    #define __cdecl
  #endif
  #ifndef __fastcall
    #define __fastcall
  #endif
#endif
#ifndef DL_IMPORT
  #define DL_IMPORT(t) t
#endif
#ifndef DL_EXPORT
  #define DL_EXPORT(t) t
#endif
#define __PYX_COMMA ,
#ifndef HAVE_LONG_LONG
  #if PY_VERSION_HEX >= 0x02070000
    #define HAVE_LONG_LONG
  #endif
#endif
#ifndef PY_LONG_LONG
  #define PY_LONG_LONG LONG_LONG
#endif
#ifndef Py_HUGE_VAL
  #define Py_HUGE_VAL HUGE_VAL
#endif
#ifdef PYPY_VERSION
  #define CYTHON_COMPILING_IN_PYPY 1
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #undef CYTHON_USE_TYPE_SLOTS
  #define CYTHON_USE_TYPE_SLOTS 0
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #if PY_VERSION_HEX < 0x03050000
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #undef CYTHON_USE_UNICODE_INTERNALS
  #define CYTHON_USE_UNICODE_INTERNALS 0
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #undef CYTHON_AVOID_BORROWED_REFS
  #define CYTHON_AVOID_BORROWED_REFS 1
  #undef CYTHON_ASSUME_SAFE_MACROS
  #define CYTHON_ASSUME_SAFE_MACROS 0
  #undef CYTHON_UNPACK_METHODS
  #define CYTHON_UNPACK_METHODS 0
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #if PY_VERSION_HEX < 0x03090000
    #undef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #elif !defined(CYTHON_PEP489_MULTI_PHASE_INIT)
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1 && PYPY_VERSION_NUM >= 0x07030C00)
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PYSTON_VERSION)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 1
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #undef CYTHON_USE_ASYNC_SLOTS
  #define CYTHON_USE_ASYNC_SLOTS 0
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #undef CYTHON_PEP489_MULTI_PHASE_INIT
  #define CYTHON_PEP489_MULTI_PHASE_INIT 0
  #undef CYTHON_USE_TP_FINALIZE
  #define CYTHON_USE_TP_FINALIZE 0
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 0
  #endif
#elif defined(PY_NOGIL)
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 0
  #define CYTHON_COMPILING_IN_NOGIL 1
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #undef CYTHON_USE_PYTYPE_LOOKUP
  #define CYTHON_USE_PYTYPE_LOOKUP 0
  #ifndef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #undef CYTHON_USE_PYLIST_INTERNALS
  #define CYTHON_USE_PYLIST_INTERNALS 0
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #undef CYTHON_USE_UNICODE_WRITER
  #define CYTHON_USE_UNICODE_WRITER 0
  #undef CYTHON_USE_PYLONG_INTERNALS
  #define CYTHON_USE_PYLONG_INTERNALS 0
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #undef CYTHON_FAST_THREAD_STATE
  #define CYTHON_FAST_THREAD_STATE 0
  #undef CYTHON_FAST_PYCALL
  #define CYTHON_FAST_PYCALL 0
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT 1
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE 1
  #endif
  #undef CYTHON_USE_DICT_VERSIONS
  #define CYTHON_USE_DICT_VERSIONS 0
  #undef CYTHON_USE_EXC_INFO_STACK
  #define CYTHON_USE_EXC_INFO_STACK 0
#else
  #define CYTHON_COMPILING_IN_PYPY 0
  #define CYTHON_COMPILING_IN_PYSTON 0
  #define CYTHON_COMPILING_IN_CPYTHON 1
  #define CYTHON_COMPILING_IN_NOGIL 0
  #ifndef CYTHON_USE_TYPE_SLOTS
    #define CYTHON_USE_TYPE_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYTYPE_LOOKUP
    #define CYTHON_USE_PYTYPE_LOOKUP 0
  #elif !defined(CYTHON_USE_PYTYPE_LOOKUP)
    #define CYTHON_USE_PYTYPE_LOOKUP 1
  #endif
  #if PY_MAJOR_VERSION < 3
    #undef CYTHON_USE_ASYNC_SLOTS
    #define CYTHON_USE_ASYNC_SLOTS 0
  #elif !defined(CYTHON_USE_ASYNC_SLOTS)
    #define CYTHON_USE_ASYNC_SLOTS 1
  #endif
  #if PY_VERSION_HEX < 0x02070000
    #undef CYTHON_USE_PYLONG_INTERNALS
    #define CYTHON_USE_PYLONG_INTERNALS 0
  #elif !defined(CYTHON_USE_PYLONG_INTERNALS)
    #define CYTHON_USE_PYLONG_INTERNALS (PY_VERSION_HEX < 0x030C00A5)
  #endif
  #ifndef CYTHON_USE_PYLIST_INTERNALS
    #define CYTHON_USE_PYLIST_INTERNALS 1
  #endif
  #ifndef CYTHON_USE_UNICODE_INTERNALS
    #define CYTHON_USE_UNICODE_INTERNALS 1
  #endif
  #if PY_VERSION_HEX < 0x030300F0 || PY_VERSION_HEX >= 0x030B00A2
    #undef CYTHON_USE_UNICODE_WRITER
    #define CYTHON_USE_UNICODE_WRITER 0
  #elif !defined(CYTHON_USE_UNICODE_WRITER)
    #define CYTHON_USE_UNICODE_WRITER 1
  #endif
  #ifndef CYTHON_AVOID_BORROWED_REFS
    #define CYTHON_AVOID_BORROWED_REFS 0
  #endif
  #ifndef CYTHON_ASSUME_SAFE_MACROS
    #define CYTHON_ASSUME_SAFE_MACROS 1
  #endif
  #ifndef CYTHON_UNPACK_METHODS
    #define CYTHON_UNPACK_METHODS 1
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_FAST_THREAD_STATE
    #define CYTHON_FAST_THREAD_STATE 0
  #elif !defined(CYTHON_FAST_THREAD_STATE)
    #define CYTHON_FAST_THREAD_STATE 1
  #endif
  #ifndef CYTHON_FAST_PYCALL
    #define CYTHON_FAST_PYCALL (PY_VERSION_HEX < 0x030A0000)
  #endif
  #ifndef CYTHON_PEP489_MULTI_PHASE_INIT
    #define CYTHON_PEP489_MULTI_PHASE_INIT (PY_VERSION_HEX >= 0x03050000)
  #endif
  #ifndef CYTHON_USE_TP_FINALIZE
    #define CYTHON_USE_TP_FINALIZE (PY_VERSION_HEX >= 0x030400a1)
  #endif
  #ifndef CYTHON_USE_DICT_VERSIONS
    #define CYTHON_USE_DICT_VERSIONS ((PY_VERSION_HEX >= 0x030600B1) && (PY_VERSION_HEX < 0x030C00A5))
  #endif
  #if PY_VERSION_HEX >= 0x030B00A4
    #undef CYTHON_USE_EXC_INFO_STACK
    #define CYTHON_USE_EXC_INFO_STACK 0
  #elif !defined(CYTHON_USE_EXC_INFO_STACK)
    #define CYTHON_USE_EXC_INFO_STACK (PY_VERSION_HEX >= 0x030700A3)
  #endif
  #ifndef CYTHON_UPDATE_DESCRIPTOR_DOC
    #define CYTHON_UPDATE_DESCRIPTOR_DOC 1
  #endif
#endif
#if !defined(CYTHON_FAST_PYCCALL)
#define CYTHON_FAST_PYCCALL  (CYTHON_FAST_PYCALL && PY_VERSION_HEX >= 0x030600B1)
#endif
#if CYTHON_USE_PYLONG_INTERNALS
  #if PY_MAJOR_VERSION < 3
    #include "longintrepr.h"
  #endif
  #undef SHIFT
  #undef BASE
  #undef MASK
  #ifdef SIZEOF_VOID_P
    enum { __pyx_check_sizeof_voidp = 1 / (int)(SIZEOF_VOID_P == sizeof(void*)) };
  #endif
#endif
#ifndef __has_attribute
  #define __has_attribute(x) 0
#endif
#ifndef __has_cpp_attribute
  #define __has_cpp_attribute(x) 0
#endif
#ifndef CYTHON_RESTRICT
  #if defined(__GNUC__)
    #define CYTHON_RESTRICT __restrict__
  #elif defined(_MSC_VER) && _MSC_VER >= 1400
    #define CYTHON_RESTRICT __restrict
  #elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define CYTHON_RESTRICT restrict
  #else
    #define CYTHON_RESTRICT
  #endif
#endif
#ifndef CYTHON_UNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define CYTHON_UNUSED __attribute__ ((__unused__))
#   else
#     define CYTHON_UNUSED
#   endif
# elif defined(__ICC) || (defined(__INTEL_COMPILER) && !defined(_MSC_VER))
#   define CYTHON_UNUSED __attribute__ ((__unused__))
# else
#   define CYTHON_UNUSED
# endif
#endif
#ifndef CYTHON_MAYBE_UNUSED_VAR
#  if defined(__cplusplus)
     template<class T> void CYTHON_MAYBE_UNUSED_VAR( const T& ) { }
#  else
#    define CYTHON_MAYBE_UNUSED_VAR(x) (void)(x)
#  endif
#endif
#ifndef CYTHON_NCP_UNUSED
# if CYTHON_COMPILING_IN_CPYTHON
#  define CYTHON_NCP_UNUSED
# else
#  define CYTHON_NCP_UNUSED CYTHON_UNUSED
# endif
#endif
#define __Pyx_void_to_None(void_result) ((void)(void_result), Py_INCREF(Py_None), Py_None)
#ifdef _MSC_VER
    #ifndef _MSC_STDINT_H_
        #if _MSC_VER < 1300
           typedef unsigned char     uint8_t;
           typedef unsigned int      uint32_t;
        #else
           typedef unsigned __int8   uint8_t;
           typedef unsigned __int32  uint32_t;
        #endif
    #endif
#else
   #include <stdint.h>
#endif
#ifndef CYTHON_FALLTHROUGH
  #if defined(__cplusplus) && __cplusplus >= 201103L
    #if __has_cpp_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH [[fallthrough]]
    #elif __has_cpp_attribute(clang::fallthrough)
      #define CYTHON_FALLTHROUGH [[clang::fallthrough]]
    #elif __has_cpp_attribute(gnu::fallthrough)
      #define CYTHON_FALLTHROUGH [[gnu::fallthrough]]
    #endif
  #endif
  #ifndef CYTHON_FALLTHROUGH
    #if __has_attribute(fallthrough)
      #define CYTHON_FALLTHROUGH __attribute__((fallthrough))
    #else
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
  #if defined(__clang__ ) && defined(__apple_build_version__)
    #if __apple_build_version__ < 7000000
      #undef  CYTHON_FALLTHROUGH
      #define CYTHON_FALLTHROUGH
    #endif
  #endif
#endif

#ifndef __cplusplus
  #error "Cython files generated with the C++ option must be compiled with a C++ compiler."
#endif
#ifndef CYTHON_INLINE
  #if defined(__clang__)
    #define CYTHON_INLINE __inline__ __attribute__ ((__unused__))
  #else
    #define CYTHON_INLINE inline
  #endif
#endif
template<typename T>
void __Pyx_call_destructor(T& x) {
    x.~T();
}
template<typename T>
class __Pyx_FakeReference {
  public:
    __Pyx_FakeReference() : ptr(NULL) { }
    __Pyx_FakeReference(const T& ref) : ptr(const_cast<T*>(&ref)) { }
    T *operator->() { return ptr; }
    T *operator&() { return ptr; }
    operator T&() { return *ptr; }
    template<typename U> bool operator ==(U other) { return *ptr == other; }
    template<typename U> bool operator !=(U other) { return *ptr != other; }
  private:
    T *ptr;
};

#define __PYX_BUILD_PY_SSIZE_T "n"
#define CYTHON_FORMAT_SSIZE_T "z"
#if PY_MAJOR_VERSION < 3
  #define __Pyx_BUILTIN_MODULE_NAME "__builtin__"
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a+k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
  #define __Pyx_DefaultClassType PyClass_Type
#else
  #define __Pyx_BUILTIN_MODULE_NAME "builtins"
  #define __Pyx_DefaultClassType PyType_Type
#if PY_VERSION_HEX >= 0x030B00A1
    static CYTHON_INLINE PyCodeObject* __Pyx_PyCode_New(int a, int k, int l, int s, int f,
                                                    PyObject *code, PyObject *c, PyObject* n, PyObject *v,
                                                    PyObject *fv, PyObject *cell, PyObject* fn,
                                                    PyObject *name, int fline, PyObject *lnos) {
        PyObject *kwds=NULL, *argcount=NULL, *posonlyargcount=NULL, *kwonlyargcount=NULL;
        PyObject *nlocals=NULL, *stacksize=NULL, *flags=NULL, *replace=NULL, *call_result=NULL, *empty=NULL;
        const char *fn_cstr=NULL;
        const char *name_cstr=NULL;
        PyCodeObject* co=NULL;
        PyObject *type, *value, *traceback;
        PyErr_Fetch(&type, &value, &traceback);
        if (!(kwds=PyDict_New())) goto end;
        if (!(argcount=PyLong_FromLong(a))) goto end;
        if (PyDict_SetItemString(kwds, "co_argcount", argcount) != 0) goto end;
        if (!(posonlyargcount=PyLong_FromLong(0))) goto end;
        if (PyDict_SetItemString(kwds, "co_posonlyargcount", posonlyargcount) != 0) goto end;
        if (!(kwonlyargcount=PyLong_FromLong(k))) goto end;
        if (PyDict_SetItemString(kwds, "co_kwonlyargcount", kwonlyargcount) != 0) goto end;
        if (!(nlocals=PyLong_FromLong(l))) goto end;
        if (PyDict_SetItemString(kwds, "co_nlocals", nlocals) != 0) goto end;
        if (!(stacksize=PyLong_FromLong(s))) goto end;
        if (PyDict_SetItemString(kwds, "co_stacksize", stacksize) != 0) goto end;
        if (!(flags=PyLong_FromLong(f))) goto end;
        if (PyDict_SetItemString(kwds, "co_flags", flags) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_code", code) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_consts", c) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_names", n) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_varnames", v) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_freevars", fv) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_cellvars", cell) != 0) goto end;
        if (PyDict_SetItemString(kwds, "co_linetable", lnos) != 0) goto end;
        if (!(fn_cstr=PyUnicode_AsUTF8AndSize(fn, NULL))) goto end;
        if (!(name_cstr=PyUnicode_AsUTF8AndSize(name, NULL))) goto end;
        if (!(co = PyCode_NewEmpty(fn_cstr, name_cstr, fline))) goto end;
        if (!(replace = PyObject_GetAttrString((PyObject*)co, "replace"))) goto cleanup_code_too;
        if (!(empty = PyTuple_New(0))) goto cleanup_code_too; // unfortunately __pyx_empty_tuple isn't available here
        if (!(call_result = PyObject_Call(replace, empty, kwds))) goto cleanup_code_too;
        Py_XDECREF((PyObject*)co);
        co = (PyCodeObject*)call_result;
        call_result = NULL;
        if (0) {
            cleanup_code_too:
            Py_XDECREF((PyObject*)co);
            co = NULL;
        }
        end:
        Py_XDECREF(kwds);
        Py_XDECREF(argcount);
        Py_XDECREF(posonlyargcount);
        Py_XDECREF(kwonlyargcount);
        Py_XDECREF(nlocals);
        Py_XDECREF(stacksize);
        Py_XDECREF(replace);
        Py_XDECREF(call_result);
        Py_XDECREF(empty);
        if (type) {
            PyErr_Restore(type, value, traceback);
        }
        return co;
    }
#else
  #define __Pyx_PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)\
          PyCode_New(a, k, l, s, f, code, c, n, v, fv, cell, fn, name, fline, lnos)
#endif
  #define __Pyx_DefaultClassType PyType_Type
#endif
#if PY_VERSION_HEX >= 0x030900F0 && !CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyObject_GC_IsFinalized(o) PyObject_GC_IsFinalized(o)
#else
  #define __Pyx_PyObject_GC_IsFinalized(o) _PyGC_FINALIZED(o)
#endif
#ifndef Py_TPFLAGS_CHECKTYPES
  #define Py_TPFLAGS_CHECKTYPES 0
#endif
#ifndef Py_TPFLAGS_HAVE_INDEX
  #define Py_TPFLAGS_HAVE_INDEX 0
#endif
#ifndef Py_TPFLAGS_HAVE_NEWBUFFER
  #define Py_TPFLAGS_HAVE_NEWBUFFER 0
#endif
#ifndef Py_TPFLAGS_HAVE_FINALIZE
  #define Py_TPFLAGS_HAVE_FINALIZE 0
#endif
#ifndef METH_STACKLESS
  #define METH_STACKLESS 0
#endif
#if PY_VERSION_HEX <= 0x030700A3 || !defined(METH_FASTCALL)
  #ifndef METH_FASTCALL
     #define METH_FASTCALL 0x80
  #endif
  typedef PyObject *(*__Pyx_PyCFunctionFast) (PyObject *self, PyObject *const *args, Py_ssize_t nargs);
  typedef PyObject *(*__Pyx_PyCFunctionFastWithKeywords) (PyObject *self, PyObject *const *args,
                                                          Py_ssize_t nargs, PyObject *kwnames);
#else
  #define __Pyx_PyCFunctionFast _PyCFunctionFast
  #define __Pyx_PyCFunctionFastWithKeywords _PyCFunctionFastWithKeywords
#endif
#if CYTHON_FAST_PYCCALL
#define __Pyx_PyFastCFunction_Check(func)\
    ((PyCFunction_Check(func) && (METH_FASTCALL == (PyCFunction_GET_FLAGS(func) & ~(METH_CLASS | METH_STATIC | METH_COEXIST | METH_KEYWORDS | METH_STACKLESS)))))
#else
#define __Pyx_PyFastCFunction_Check(func) 0
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Malloc)
  #define PyObject_Malloc(s)   PyMem_Malloc(s)
  #define PyObject_Free(p)     PyMem_Free(p)
  #define PyObject_Realloc(p)  PyMem_Realloc(p)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030400A1
  #define PyMem_RawMalloc(n)           PyMem_Malloc(n)
  #define PyMem_RawRealloc(p, n)       PyMem_Realloc(p, n)
  #define PyMem_RawFree(p)             PyMem_Free(p)
#endif
#if CYTHON_COMPILING_IN_PYSTON
  #define __Pyx_PyCode_HasFreeVars(co)  PyCode_HasFreeVars(co)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno) PyFrame_SetLineNumber(frame, lineno)
#else
  #define __Pyx_PyCode_HasFreeVars(co)  (PyCode_GetNumFree(co) > 0)
  #define __Pyx_PyFrame_SetLineNumber(frame, lineno)  (frame)->f_lineno = (lineno)
#endif
#if !CYTHON_FAST_THREAD_STATE || PY_VERSION_HEX < 0x02070000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#elif PY_VERSION_HEX >= 0x03060000
  #define __Pyx_PyThreadState_Current _PyThreadState_UncheckedGet()
#elif PY_VERSION_HEX >= 0x03000000
  #define __Pyx_PyThreadState_Current PyThreadState_GET()
#else
  #define __Pyx_PyThreadState_Current _PyThreadState_Current
#endif
#if PY_VERSION_HEX < 0x030700A2 && !defined(PyThread_tss_create) && !defined(Py_tss_NEEDS_INIT)
#include "pythread.h"
#define Py_tss_NEEDS_INIT 0
typedef int Py_tss_t;
static CYTHON_INLINE int PyThread_tss_create(Py_tss_t *key) {
  *key = PyThread_create_key();
  return 0;
}
static CYTHON_INLINE Py_tss_t * PyThread_tss_alloc(void) {
  Py_tss_t *key = (Py_tss_t *)PyObject_Malloc(sizeof(Py_tss_t));
  *key = Py_tss_NEEDS_INIT;
  return key;
}
static CYTHON_INLINE void PyThread_tss_free(Py_tss_t *key) {
  PyObject_Free(key);
}
static CYTHON_INLINE int PyThread_tss_is_created(Py_tss_t *key) {
  return *key != Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE void PyThread_tss_delete(Py_tss_t *key) {
  PyThread_delete_key(*key);
  *key = Py_tss_NEEDS_INIT;
}
static CYTHON_INLINE int PyThread_tss_set(Py_tss_t *key, void *value) {
  return PyThread_set_key_value(*key, value);
}
static CYTHON_INLINE void * PyThread_tss_get(Py_tss_t *key) {
  return PyThread_get_key_value(*key);
}
#endif
#if CYTHON_COMPILING_IN_CPYTHON || defined(_PyDict_NewPresized)
#define __Pyx_PyDict_NewPresized(n)  ((n <= 8) ? PyDict_New() : _PyDict_NewPresized(n))
#else
#define __Pyx_PyDict_NewPresized(n)  PyDict_New()
#endif
#if PY_MAJOR_VERSION >= 3 || CYTHON_FUTURE_DIVISION
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_TrueDivide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceTrueDivide(x,y)
#else
  #define __Pyx_PyNumber_Divide(x,y)         PyNumber_Divide(x,y)
  #define __Pyx_PyNumber_InPlaceDivide(x,y)  PyNumber_InPlaceDivide(x,y)
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x030500A1 && CYTHON_USE_UNICODE_INTERNALS
#define __Pyx_PyDict_GetItemStr(dict, name)  _PyDict_GetItem_KnownHash(dict, name, ((PyASCIIObject *) name)->hash)
#else
#define __Pyx_PyDict_GetItemStr(dict, name)  PyDict_GetItem(dict, name)
#endif
#if PY_VERSION_HEX > 0x03030000 && defined(PyUnicode_KIND)
  #define CYTHON_PEP393_ENABLED 1
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_READY(op)       (0)
  #else
    #define __Pyx_PyUnicode_READY(op)       (likely(PyUnicode_IS_READY(op)) ?\
                                                0 : _PyUnicode_Ready((PyObject *)(op)))
  #endif
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_LENGTH(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) PyUnicode_READ_CHAR(u, i)
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   PyUnicode_MAX_CHAR_VALUE(u)
  #define __Pyx_PyUnicode_KIND(u)         PyUnicode_KIND(u)
  #define __Pyx_PyUnicode_DATA(u)         PyUnicode_DATA(u)
  #define __Pyx_PyUnicode_READ(k, d, i)   PyUnicode_READ(k, d, i)
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  PyUnicode_WRITE(k, d, i, ch)
  #if PY_VERSION_HEX >= 0x030C0000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_LENGTH(u))
  #else
    #if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX >= 0x03090000
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : ((PyCompactUnicodeObject *)(u))->wstr_length))
    #else
    #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != (likely(PyUnicode_IS_READY(u)) ? PyUnicode_GET_LENGTH(u) : PyUnicode_GET_SIZE(u)))
    #endif
  #endif
#else
  #define CYTHON_PEP393_ENABLED 0
  #define PyUnicode_1BYTE_KIND  1
  #define PyUnicode_2BYTE_KIND  2
  #define PyUnicode_4BYTE_KIND  4
  #define __Pyx_PyUnicode_READY(op)       (0)
  #define __Pyx_PyUnicode_GET_LENGTH(u)   PyUnicode_GET_SIZE(u)
  #define __Pyx_PyUnicode_READ_CHAR(u, i) ((Py_UCS4)(PyUnicode_AS_UNICODE(u)[i]))
  #define __Pyx_PyUnicode_MAX_CHAR_VALUE(u)   ((sizeof(Py_UNICODE) == 2) ? 65535 : 1114111)
  #define __Pyx_PyUnicode_KIND(u)         (sizeof(Py_UNICODE))
  #define __Pyx_PyUnicode_DATA(u)         ((void*)PyUnicode_AS_UNICODE(u))
  #define __Pyx_PyUnicode_READ(k, d, i)   ((void)(k), (Py_UCS4)(((Py_UNICODE*)d)[i]))
  #define __Pyx_PyUnicode_WRITE(k, d, i, ch)  (((void)(k)), ((Py_UNICODE*)d)[i] = ch)
  #define __Pyx_PyUnicode_IS_TRUE(u)      (0 != PyUnicode_GET_SIZE(u))
#endif
#if CYTHON_COMPILING_IN_PYPY
  #define __Pyx_PyUnicode_Concat(a, b)      PyNumber_Add(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  PyNumber_Add(a, b)
#else
  #define __Pyx_PyUnicode_Concat(a, b)      PyUnicode_Concat(a, b)
  #define __Pyx_PyUnicode_ConcatSafe(a, b)  ((unlikely((a) == Py_None) || unlikely((b) == Py_None)) ?\
      PyNumber_Add(a, b) : __Pyx_PyUnicode_Concat(a, b))
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyUnicode_Contains)
  #define PyUnicode_Contains(u, s)  PySequence_Contains(u, s)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyByteArray_Check)
  #define PyByteArray_Check(obj)  PyObject_TypeCheck(obj, &PyByteArray_Type)
#endif
#if CYTHON_COMPILING_IN_PYPY && !defined(PyObject_Format)
  #define PyObject_Format(obj, fmt)  PyObject_CallMethod(obj, "__format__", "O", fmt)
#endif
#define __Pyx_PyString_FormatSafe(a, b)   ((unlikely((a) == Py_None || (PyString_Check(b) && !PyString_CheckExact(b)))) ? PyNumber_Remainder(a, b) : __Pyx_PyString_Format(a, b))
#define __Pyx_PyUnicode_FormatSafe(a, b)  ((unlikely((a) == Py_None || (PyUnicode_Check(b) && !PyUnicode_CheckExact(b)))) ? PyNumber_Remainder(a, b) : PyUnicode_Format(a, b))
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyString_Format(a, b)  PyUnicode_Format(a, b)
#else
  #define __Pyx_PyString_Format(a, b)  PyString_Format(a, b)
#endif
#if PY_MAJOR_VERSION < 3 && !defined(PyObject_ASCII)
  #define PyObject_ASCII(o)            PyObject_Repr(o)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBaseString_Type            PyUnicode_Type
  #define PyStringObject               PyUnicodeObject
  #define PyString_Type                PyUnicode_Type
  #define PyString_Check               PyUnicode_Check
  #define PyString_CheckExact          PyUnicode_CheckExact
#ifndef PyObject_Unicode
  #define PyObject_Unicode             PyObject_Str
#endif
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyBaseString_Check(obj) PyUnicode_Check(obj)
  #define __Pyx_PyBaseString_CheckExact(obj) PyUnicode_CheckExact(obj)
#else
  #define __Pyx_PyBaseString_Check(obj) (PyString_Check(obj) || PyUnicode_Check(obj))
  #define __Pyx_PyBaseString_CheckExact(obj) (PyString_CheckExact(obj) || PyUnicode_CheckExact(obj))
#endif
#ifndef PySet_CheckExact
  #define PySet_CheckExact(obj)        (Py_TYPE(obj) == &PySet_Type)
#endif
#if PY_VERSION_HEX >= 0x030900A4
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_SET_REFCNT(obj, refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SET_SIZE(obj, size)
#else
  #define __Pyx_SET_REFCNT(obj, refcnt) Py_REFCNT(obj) = (refcnt)
  #define __Pyx_SET_SIZE(obj, size) Py_SIZE(obj) = (size)
#endif
#if CYTHON_ASSUME_SAFE_MACROS
  #define __Pyx_PySequence_SIZE(seq)  Py_SIZE(seq)
#else
  #define __Pyx_PySequence_SIZE(seq)  PySequence_Size(seq)
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyIntObject                  PyLongObject
  #define PyInt_Type                   PyLong_Type
  #define PyInt_Check(op)              PyLong_Check(op)
  #define PyInt_CheckExact(op)         PyLong_CheckExact(op)
  #define PyInt_FromString             PyLong_FromString
  #define PyInt_FromUnicode            PyLong_FromUnicode
  #define PyInt_FromLong               PyLong_FromLong
  #define PyInt_FromSize_t             PyLong_FromSize_t
  #define PyInt_FromSsize_t            PyLong_FromSsize_t
  #define PyInt_AsLong                 PyLong_AsLong
  #define PyInt_AS_LONG                PyLong_AS_LONG
  #define PyInt_AsSsize_t              PyLong_AsSsize_t
  #define PyInt_AsUnsignedLongMask     PyLong_AsUnsignedLongMask
  #define PyInt_AsUnsignedLongLongMask PyLong_AsUnsignedLongLongMask
  #define PyNumber_Int                 PyNumber_Long
#endif
#if PY_MAJOR_VERSION >= 3
  #define PyBoolObject                 PyLongObject
#endif
#if PY_MAJOR_VERSION >= 3 && CYTHON_COMPILING_IN_PYPY
  #ifndef PyUnicode_InternFromString
    #define PyUnicode_InternFromString(s) PyUnicode_FromString(s)
  #endif
#endif
#if PY_VERSION_HEX < 0x030200A4
  typedef long Py_hash_t;
  #define __Pyx_PyInt_FromHash_t PyInt_FromLong
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsHash_t
#else
  #define __Pyx_PyInt_FromHash_t PyInt_FromSsize_t
  #define __Pyx_PyInt_AsHash_t   __Pyx_PyIndex_AsSsize_t
#endif
#if PY_MAJOR_VERSION >= 3
  #define __Pyx_PyMethod_New(func, self, klass) ((self) ? ((void)(klass), PyMethod_New(func, self)) : __Pyx_NewRef(func))
#else
  #define __Pyx_PyMethod_New(func, self, klass) PyMethod_New(func, self, klass)
#endif
#if CYTHON_USE_ASYNC_SLOTS
  #if PY_VERSION_HEX >= 0x030500B1
    #define __Pyx_PyAsyncMethodsStruct PyAsyncMethods
    #define __Pyx_PyType_AsAsync(obj) (Py_TYPE(obj)->tp_as_async)
  #else
    #define __Pyx_PyType_AsAsync(obj) ((__Pyx_PyAsyncMethodsStruct*) (Py_TYPE(obj)->tp_reserved))
  #endif
#else
  #define __Pyx_PyType_AsAsync(obj) NULL
#endif
#ifndef __Pyx_PyAsyncMethodsStruct
    typedef struct {
        unaryfunc am_await;
        unaryfunc am_aiter;
        unaryfunc am_anext;
    } __Pyx_PyAsyncMethodsStruct;
#endif

#if defined(_WIN32) || defined(WIN32) || defined(MS_WINDOWS)
  #if !defined(_USE_MATH_DEFINES)
    #define _USE_MATH_DEFINES
  #endif
#endif
#include <math.h>
#ifdef NAN
#define __PYX_NAN() ((float) NAN)
#else
static CYTHON_INLINE float __PYX_NAN() {
  float value;
  memset(&value, 0xFF, sizeof(value));
  return value;
}
#endif
#if defined(__CYGWIN__) && defined(_LDBL_EQ_DBL)
#define __Pyx_truncl trunc
#else
#define __Pyx_truncl truncl
#endif

#define __PYX_MARK_ERR_POS(f_index, lineno) \
    { __pyx_filename = __pyx_f[f_index]; (void)__pyx_filename; __pyx_lineno = lineno; (void)__pyx_lineno; __pyx_clineno = __LINE__; (void)__pyx_clineno; }
#define __PYX_ERR(f_index, lineno, Ln_error) \
    { __PYX_MARK_ERR_POS(f_index, lineno) goto Ln_error; }

#ifndef __PYX_EXTERN_C
  #ifdef __cplusplus
    #define __PYX_EXTERN_C extern "C"
  #else
    #define __PYX_EXTERN_C extern
  #endif
#endif

#define __PYX_HAVE__spacy__pipeline___parser_internals___state
#define __PYX_HAVE_API__spacy__pipeline___parser_internals___state
/* Early includes */
#include <string.h>
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include "ios"
#include "new"
#include "stdexcept"
#include "typeinfo"
#include <utility>

    #if __cplusplus >= 201103L || (defined(_MSC_VER) && _MSC_VER >= 1600)
    // move should be defined for these versions of MSVC, but __cplusplus isn't set usefully
    #include <type_traits>

    namespace cython_std {
    template <typename T> typename std::remove_reference<T>::type&& move(T& t) noexcept { return std::move(t); }
    template <typename T> typename std::remove_reference<T>::type&& move(T&& t) noexcept { return std::move(t); }
    }

    #endif
    
#include <set>
#include <unordered_map>
#include <vector>
#include "numpy/arrayobject.h"
#include "numpy/ndarrayobject.h"
#include "numpy/ndarraytypes.h"
#include "numpy/arrayscalars.h"
#include "numpy/ufuncobject.h"

    /* NumPy API declarations from "numpy/__init__.pxd" */
    
#include <unordered_set>
#ifdef _OPENMP
#include <omp.h>
#endif /* _OPENMP */

#if defined(PYREX_WITHOUT_ASSERTIONS) && !defined(CYTHON_WITHOUT_ASSERTIONS)
#define CYTHON_WITHOUT_ASSERTIONS
#endif

typedef struct {PyObject **p; const char *s; const Py_ssize_t n; const char* encoding;
                const char is_unicode; const char is_str; const char intern; } __Pyx_StringTabEntry;

#define __PYX_DEFAULT_STRING_ENCODING_IS_ASCII 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_UTF8 0
#define __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT (PY_MAJOR_VERSION >= 3 && __PYX_DEFAULT_STRING_ENCODING_IS_UTF8)
#define __PYX_DEFAULT_STRING_ENCODING ""
#define __Pyx_PyObject_FromString __Pyx_PyBytes_FromString
#define __Pyx_PyObject_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#define __Pyx_uchar_cast(c) ((unsigned char)c)
#define __Pyx_long_cast(x) ((long)x)
#define __Pyx_fits_Py_ssize_t(v, type, is_signed)  (\
    (sizeof(type) < sizeof(Py_ssize_t))  ||\
    (sizeof(type) > sizeof(Py_ssize_t) &&\
          likely(v < (type)PY_SSIZE_T_MAX ||\
                 v == (type)PY_SSIZE_T_MAX)  &&\
          (!is_signed || likely(v > (type)PY_SSIZE_T_MIN ||\
                                v == (type)PY_SSIZE_T_MIN)))  ||\
    (sizeof(type) == sizeof(Py_ssize_t) &&\
          (is_signed || likely(v < (type)PY_SSIZE_T_MAX ||\
                               v == (type)PY_SSIZE_T_MAX)))  )
static CYTHON_INLINE int __Pyx_is_valid_index(Py_ssize_t i, Py_ssize_t limit) {
    return (size_t) i < (size_t) limit;
}
#if defined (__cplusplus) && __cplusplus >= 201103L
    #include <cstdlib>
    #define __Pyx_sst_abs(value) std::abs(value)
#elif SIZEOF_INT >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) abs(value)
#elif SIZEOF_LONG >= SIZEOF_SIZE_T
    #define __Pyx_sst_abs(value) labs(value)
#elif defined (_MSC_VER)
    #define __Pyx_sst_abs(value) ((Py_ssize_t)_abs64(value))
#elif defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L
    #define __Pyx_sst_abs(value) llabs(value)
#elif defined (__GNUC__)
    #define __Pyx_sst_abs(value) __builtin_llabs(value)
#else
    #define __Pyx_sst_abs(value) ((value<0) ? -value : value)
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject*);
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject*, Py_ssize_t* length);
#define __Pyx_PyByteArray_FromString(s) PyByteArray_FromStringAndSize((const char*)s, strlen((const char*)s))
#define __Pyx_PyByteArray_FromStringAndSize(s, l) PyByteArray_FromStringAndSize((const char*)s, l)
#define __Pyx_PyBytes_FromString        PyBytes_FromString
#define __Pyx_PyBytes_FromStringAndSize PyBytes_FromStringAndSize
static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char*);
#if PY_MAJOR_VERSION < 3
    #define __Pyx_PyStr_FromString        __Pyx_PyBytes_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyBytes_FromStringAndSize
#else
    #define __Pyx_PyStr_FromString        __Pyx_PyUnicode_FromString
    #define __Pyx_PyStr_FromStringAndSize __Pyx_PyUnicode_FromStringAndSize
#endif
#define __Pyx_PyBytes_AsWritableString(s)     ((char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableSString(s)    ((signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsWritableUString(s)    ((unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsString(s)     ((const char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsSString(s)    ((const signed char*) PyBytes_AS_STRING(s))
#define __Pyx_PyBytes_AsUString(s)    ((const unsigned char*) PyBytes_AS_STRING(s))
#define __Pyx_PyObject_AsWritableString(s)    ((char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableSString(s)    ((signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsWritableUString(s)    ((unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsSString(s)    ((const signed char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_AsUString(s)    ((const unsigned char*) __Pyx_PyObject_AsString(s))
#define __Pyx_PyObject_FromCString(s)  __Pyx_PyObject_FromString((const char*)s)
#define __Pyx_PyBytes_FromCString(s)   __Pyx_PyBytes_FromString((const char*)s)
#define __Pyx_PyByteArray_FromCString(s)   __Pyx_PyByteArray_FromString((const char*)s)
#define __Pyx_PyStr_FromCString(s)     __Pyx_PyStr_FromString((const char*)s)
#define __Pyx_PyUnicode_FromCString(s) __Pyx_PyUnicode_FromString((const char*)s)
static CYTHON_INLINE size_t __Pyx_Py_UNICODE_strlen(const Py_UNICODE *u) {
    const Py_UNICODE *u_end = u;
    while (*u_end++) ;
    return (size_t)(u_end - u - 1);
}
#define __Pyx_PyUnicode_FromUnicode(u)       PyUnicode_FromUnicode(u, __Pyx_Py_UNICODE_strlen(u))
#define __Pyx_PyUnicode_FromUnicodeAndLength PyUnicode_FromUnicode
#define __Pyx_PyUnicode_AsUnicode            PyUnicode_AsUnicode
#define __Pyx_NewRef(obj) (Py_INCREF(obj), obj)
#define __Pyx_Owned_Py_None(b) __Pyx_NewRef(Py_None)
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b);
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject*);
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject*);
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x);
#define __Pyx_PySequence_Tuple(obj)\
    (likely(PyTuple_CheckExact(obj)) ? __Pyx_NewRef(obj) : PySequence_Tuple(obj))
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject*);
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t);
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject*);
#if CYTHON_ASSUME_SAFE_MACROS
#define __pyx_PyFloat_AsDouble(x) (PyFloat_CheckExact(x) ? PyFloat_AS_DOUBLE(x) : PyFloat_AsDouble(x))
#else
#define __pyx_PyFloat_AsDouble(x) PyFloat_AsDouble(x)
#endif
#define __pyx_PyFloat_AsFloat(x) ((float) __pyx_PyFloat_AsDouble(x))
#if PY_MAJOR_VERSION >= 3
#define __Pyx_PyNumber_Int(x) (PyLong_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Long(x))
#else
#define __Pyx_PyNumber_Int(x) (PyInt_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Int(x))
#endif
#define __Pyx_PyNumber_Float(x) (PyFloat_CheckExact(x) ? __Pyx_NewRef(x) : PyNumber_Float(x))
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
static int __Pyx_sys_getdefaultencoding_not_ascii;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    PyObject* ascii_chars_u = NULL;
    PyObject* ascii_chars_b = NULL;
    const char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    if (strcmp(default_encoding_c, "ascii") == 0) {
        __Pyx_sys_getdefaultencoding_not_ascii = 0;
    } else {
        char ascii_chars[128];
        int c;
        for (c = 0; c < 128; c++) {
            ascii_chars[c] = c;
        }
        __Pyx_sys_getdefaultencoding_not_ascii = 1;
        ascii_chars_u = PyUnicode_DecodeASCII(ascii_chars, 128, NULL);
        if (!ascii_chars_u) goto bad;
        ascii_chars_b = PyUnicode_AsEncodedString(ascii_chars_u, default_encoding_c, NULL);
        if (!ascii_chars_b || !PyBytes_Check(ascii_chars_b) || memcmp(ascii_chars, PyBytes_AS_STRING(ascii_chars_b), 128) != 0) {
            PyErr_Format(
                PyExc_ValueError,
                "This module compiled with c_string_encoding=ascii, but default encoding '%.200s' is not a superset of ascii.",
                default_encoding_c);
            goto bad;
        }
        Py_DECREF(ascii_chars_u);
        Py_DECREF(ascii_chars_b);
    }
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    Py_XDECREF(ascii_chars_u);
    Py_XDECREF(ascii_chars_b);
    return -1;
}
#endif
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT && PY_MAJOR_VERSION >= 3
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_DecodeUTF8(c_str, size, NULL)
#else
#define __Pyx_PyUnicode_FromStringAndSize(c_str, size) PyUnicode_Decode(c_str, size, __PYX_DEFAULT_STRING_ENCODING, NULL)
#if __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
static char* __PYX_DEFAULT_STRING_ENCODING;
static int __Pyx_init_sys_getdefaultencoding_params(void) {
    PyObject* sys;
    PyObject* default_encoding = NULL;
    char* default_encoding_c;
    sys = PyImport_ImportModule("sys");
    if (!sys) goto bad;
    default_encoding = PyObject_CallMethod(sys, (char*) (const char*) "getdefaultencoding", NULL);
    Py_DECREF(sys);
    if (!default_encoding) goto bad;
    default_encoding_c = PyBytes_AsString(default_encoding);
    if (!default_encoding_c) goto bad;
    __PYX_DEFAULT_STRING_ENCODING = (char*) malloc(strlen(default_encoding_c) + 1);
    if (!__PYX_DEFAULT_STRING_ENCODING) goto bad;
    strcpy(__PYX_DEFAULT_STRING_ENCODING, default_encoding_c);
    Py_DECREF(default_encoding);
    return 0;
bad:
    Py_XDECREF(default_encoding);
    return -1;
}
#endif
#endif


/* Test for GCC > 2.95 */
#if defined(__GNUC__)     && (__GNUC__ > 2 || (__GNUC__ == 2 && (__GNUC_MINOR__ > 95)))
  #define likely(x)   __builtin_expect(!!(x), 1)
  #define unlikely(x) __builtin_expect(!!(x), 0)
#else /* !__GNUC__ or GCC < 2.95 */
  #define likely(x)   (x)
  #define unlikely(x) (x)
#endif /* __GNUC__ */
static CYTHON_INLINE void __Pyx_pretend_to_initialize(void* ptr) { (void)ptr; }

static PyObject *__pyx_m = NULL;
static PyObject *__pyx_d;
static PyObject *__pyx_b;
static PyObject *__pyx_cython_runtime = NULL;
static PyObject *__pyx_empty_tuple;
static PyObject *__pyx_empty_bytes;
static PyObject *__pyx_empty_unicode;
static int __pyx_lineno;
static int __pyx_clineno = 0;
static const char * __pyx_cfilenm= __FILE__;
static const char *__pyx_filename;

/* Header.proto */
#if !defined(CYTHON_CCOMPLEX)
  #if defined(__cplusplus)
    #define CYTHON_CCOMPLEX 1
  #elif defined(_Complex_I)
    #define CYTHON_CCOMPLEX 1
  #else
    #define CYTHON_CCOMPLEX 0
  #endif
#endif
#if CYTHON_CCOMPLEX
  #ifdef __cplusplus
    #include <complex>
  #else
    #include <complex.h>
  #endif
#endif
#if CYTHON_CCOMPLEX && !defined(__cplusplus) && defined(__sun__) && defined(__GNUC__)
  #undef _Complex_I
  #define _Complex_I 1.0fj
#endif


static const char *__pyx_f[] = {
  "spacy\\pipeline\\_parser_internals\\_state.pxd",
  "__init__.pxd",
  "spacy\\lexeme.pxd",
  "spacy\\pipeline\\_parser_internals\\_state.pyx",
  "type.pxd",
  "cymem.pxd",
  "maps.pxd",
  "spacy\\strings.pxd",
  "spacy\\morphology.pxd",
  "spacy\\vocab.pxd",
};
/* ForceInitThreads.proto */
#ifndef __PYX_FORCE_INIT_THREADS
  #define __PYX_FORCE_INIT_THREADS 0
#endif

/* NoFastGil.proto */
#define __Pyx_PyGILState_Ensure PyGILState_Ensure
#define __Pyx_PyGILState_Release PyGILState_Release
#define __Pyx_FastGIL_Remember()
#define __Pyx_FastGIL_Forget()
#define __Pyx_FastGilFuncInit()


/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":688
 * # in Cython to enable them only on the right systems.
 * 
 * ctypedef npy_int8       int8_t             # <<<<<<<<<<<<<<
 * ctypedef npy_int16      int16_t
 * ctypedef npy_int32      int32_t
 */
typedef npy_int8 __pyx_t_5numpy_int8_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":689
 * 
 * ctypedef npy_int8       int8_t
 * ctypedef npy_int16      int16_t             # <<<<<<<<<<<<<<
 * ctypedef npy_int32      int32_t
 * ctypedef npy_int64      int64_t
 */
typedef npy_int16 __pyx_t_5numpy_int16_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":690
 * ctypedef npy_int8       int8_t
 * ctypedef npy_int16      int16_t
 * ctypedef npy_int32      int32_t             # <<<<<<<<<<<<<<
 * ctypedef npy_int64      int64_t
 * #ctypedef npy_int96      int96_t
 */
typedef npy_int32 __pyx_t_5numpy_int32_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":691
 * ctypedef npy_int16      int16_t
 * ctypedef npy_int32      int32_t
 * ctypedef npy_int64      int64_t             # <<<<<<<<<<<<<<
 * #ctypedef npy_int96      int96_t
 * #ctypedef npy_int128     int128_t
 */
typedef npy_int64 __pyx_t_5numpy_int64_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":695
 * #ctypedef npy_int128     int128_t
 * 
 * ctypedef npy_uint8      uint8_t             # <<<<<<<<<<<<<<
 * ctypedef npy_uint16     uint16_t
 * ctypedef npy_uint32     uint32_t
 */
typedef npy_uint8 __pyx_t_5numpy_uint8_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":696
 * 
 * ctypedef npy_uint8      uint8_t
 * ctypedef npy_uint16     uint16_t             # <<<<<<<<<<<<<<
 * ctypedef npy_uint32     uint32_t
 * ctypedef npy_uint64     uint64_t
 */
typedef npy_uint16 __pyx_t_5numpy_uint16_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":697
 * ctypedef npy_uint8      uint8_t
 * ctypedef npy_uint16     uint16_t
 * ctypedef npy_uint32     uint32_t             # <<<<<<<<<<<<<<
 * ctypedef npy_uint64     uint64_t
 * #ctypedef npy_uint96     uint96_t
 */
typedef npy_uint32 __pyx_t_5numpy_uint32_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":698
 * ctypedef npy_uint16     uint16_t
 * ctypedef npy_uint32     uint32_t
 * ctypedef npy_uint64     uint64_t             # <<<<<<<<<<<<<<
 * #ctypedef npy_uint96     uint96_t
 * #ctypedef npy_uint128    uint128_t
 */
typedef npy_uint64 __pyx_t_5numpy_uint64_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":702
 * #ctypedef npy_uint128    uint128_t
 * 
 * ctypedef npy_float32    float32_t             # <<<<<<<<<<<<<<
 * ctypedef npy_float64    float64_t
 * #ctypedef npy_float80    float80_t
 */
typedef npy_float32 __pyx_t_5numpy_float32_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":703
 * 
 * ctypedef npy_float32    float32_t
 * ctypedef npy_float64    float64_t             # <<<<<<<<<<<<<<
 * #ctypedef npy_float80    float80_t
 * #ctypedef npy_float128   float128_t
 */
typedef npy_float64 __pyx_t_5numpy_float64_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":712
 * # The int types are mapped a bit surprising --
 * # numpy.int corresponds to 'l' and numpy.long to 'q'
 * ctypedef npy_long       int_t             # <<<<<<<<<<<<<<
 * ctypedef npy_longlong   longlong_t
 * 
 */
typedef npy_long __pyx_t_5numpy_int_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":713
 * # numpy.int corresponds to 'l' and numpy.long to 'q'
 * ctypedef npy_long       int_t
 * ctypedef npy_longlong   longlong_t             # <<<<<<<<<<<<<<
 * 
 * ctypedef npy_ulong      uint_t
 */
typedef npy_longlong __pyx_t_5numpy_longlong_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":715
 * ctypedef npy_longlong   longlong_t
 * 
 * ctypedef npy_ulong      uint_t             # <<<<<<<<<<<<<<
 * ctypedef npy_ulonglong  ulonglong_t
 * 
 */
typedef npy_ulong __pyx_t_5numpy_uint_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":716
 * 
 * ctypedef npy_ulong      uint_t
 * ctypedef npy_ulonglong  ulonglong_t             # <<<<<<<<<<<<<<
 * 
 * ctypedef npy_intp       intp_t
 */
typedef npy_ulonglong __pyx_t_5numpy_ulonglong_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":718
 * ctypedef npy_ulonglong  ulonglong_t
 * 
 * ctypedef npy_intp       intp_t             # <<<<<<<<<<<<<<
 * ctypedef npy_uintp      uintp_t
 * 
 */
typedef npy_intp __pyx_t_5numpy_intp_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":719
 * 
 * ctypedef npy_intp       intp_t
 * ctypedef npy_uintp      uintp_t             # <<<<<<<<<<<<<<
 * 
 * ctypedef npy_double     float_t
 */
typedef npy_uintp __pyx_t_5numpy_uintp_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":721
 * ctypedef npy_uintp      uintp_t
 * 
 * ctypedef npy_double     float_t             # <<<<<<<<<<<<<<
 * ctypedef npy_double     double_t
 * ctypedef npy_longdouble longdouble_t
 */
typedef npy_double __pyx_t_5numpy_float_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":722
 * 
 * ctypedef npy_double     float_t
 * ctypedef npy_double     double_t             # <<<<<<<<<<<<<<
 * ctypedef npy_longdouble longdouble_t
 * 
 */
typedef npy_double __pyx_t_5numpy_double_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":723
 * ctypedef npy_double     float_t
 * ctypedef npy_double     double_t
 * ctypedef npy_longdouble longdouble_t             # <<<<<<<<<<<<<<
 * 
 * ctypedef npy_cfloat      cfloat_t
 */
typedef npy_longdouble __pyx_t_5numpy_longdouble_t;

/* "preshed/maps.pxd":5
 * 
 * 
 * ctypedef uint64_t key_t             # <<<<<<<<<<<<<<
 * 
 * 
 */
typedef uint64_t __pyx_t_7preshed_4maps_key_t;

/* "typedefs.pxd":3
 * from libc.stdint cimport int32_t, uint8_t, uint16_t, uint32_t, uint64_t, uintptr_t
 * 
 * ctypedef float weight_t             # <<<<<<<<<<<<<<
 * ctypedef uint64_t hash_t
 * ctypedef uint64_t class_t
 */
typedef float __pyx_t_5spacy_8typedefs_weight_t;

/* "typedefs.pxd":4
 * 
 * ctypedef float weight_t
 * ctypedef uint64_t hash_t             # <<<<<<<<<<<<<<
 * ctypedef uint64_t class_t
 * ctypedef uint64_t attr_t
 */
typedef uint64_t __pyx_t_5spacy_8typedefs_hash_t;

/* "typedefs.pxd":5
 * ctypedef float weight_t
 * ctypedef uint64_t hash_t
 * ctypedef uint64_t class_t             # <<<<<<<<<<<<<<
 * ctypedef uint64_t attr_t
 * ctypedef uint64_t flags_t
 */
typedef uint64_t __pyx_t_5spacy_8typedefs_class_t;

/* "typedefs.pxd":6
 * ctypedef uint64_t hash_t
 * ctypedef uint64_t class_t
 * ctypedef uint64_t attr_t             # <<<<<<<<<<<<<<
 * ctypedef uint64_t flags_t
 * ctypedef uint16_t len_t
 */
typedef uint64_t __pyx_t_5spacy_8typedefs_attr_t;

/* "typedefs.pxd":7
 * ctypedef uint64_t class_t
 * ctypedef uint64_t attr_t
 * ctypedef uint64_t flags_t             # <<<<<<<<<<<<<<
 * ctypedef uint16_t len_t
 * ctypedef uint16_t tag_t
 */
typedef uint64_t __pyx_t_5spacy_8typedefs_flags_t;

/* "typedefs.pxd":8
 * ctypedef uint64_t attr_t
 * ctypedef uint64_t flags_t
 * ctypedef uint16_t len_t             # <<<<<<<<<<<<<<
 * ctypedef uint16_t tag_t
 */
typedef uint16_t __pyx_t_5spacy_8typedefs_len_t;

/* "typedefs.pxd":9
 * ctypedef uint64_t flags_t
 * ctypedef uint16_t len_t
 * ctypedef uint16_t tag_t             # <<<<<<<<<<<<<<
 */
typedef uint16_t __pyx_t_5spacy_8typedefs_tag_t;
/* Declarations.proto */
#if CYTHON_CCOMPLEX
  #ifdef __cplusplus
    typedef ::std::complex< float > __pyx_t_float_complex;
  #else
    typedef float _Complex __pyx_t_float_complex;
  #endif
#else
    typedef struct { float real, imag; } __pyx_t_float_complex;
#endif
static CYTHON_INLINE __pyx_t_float_complex __pyx_t_float_complex_from_parts(float, float);

/* Declarations.proto */
#if CYTHON_CCOMPLEX
  #ifdef __cplusplus
    typedef ::std::complex< double > __pyx_t_double_complex;
  #else
    typedef double _Complex __pyx_t_double_complex;
  #endif
#else
    typedef struct { double real, imag; } __pyx_t_double_complex;
#endif
static CYTHON_INLINE __pyx_t_double_complex __pyx_t_double_complex_from_parts(double, double);


/*--- Type declarations ---*/
struct __pyx_obj_5cymem_5cymem_PyMalloc;
struct __pyx_obj_5cymem_5cymem_PyFree;
struct __pyx_obj_5cymem_5cymem_Pool;
struct __pyx_obj_5cymem_5cymem_Address;
struct __pyx_obj_7preshed_4maps_PreshMap;
struct __pyx_obj_7preshed_4maps_PreshMapArray;
struct __pyx_obj_5spacy_7strings_StringStore;
struct __pyx_obj_5spacy_10morphology_Morphology;
struct __pyx_obj_5spacy_5vocab_Vocab;
struct __pyx_obj_5spacy_6lexeme_Lexeme;

/* "symbols.pxd":1
 * cdef enum symbol_t:             # <<<<<<<<<<<<<<
 *     NIL
 *     IS_ALPHA
 */
enum __pyx_t_5spacy_7symbols_symbol_t {
  __pyx_e_5spacy_7symbols_NIL,
  __pyx_e_5spacy_7symbols_IS_ALPHA,
  __pyx_e_5spacy_7symbols_IS_ASCII,
  __pyx_e_5spacy_7symbols_IS_DIGIT,
  __pyx_e_5spacy_7symbols_IS_LOWER,
  __pyx_e_5spacy_7symbols_IS_PUNCT,
  __pyx_e_5spacy_7symbols_IS_SPACE,
  __pyx_e_5spacy_7symbols_IS_TITLE,
  __pyx_e_5spacy_7symbols_IS_UPPER,
  __pyx_e_5spacy_7symbols_LIKE_URL,
  __pyx_e_5spacy_7symbols_LIKE_NUM,
  __pyx_e_5spacy_7symbols_LIKE_EMAIL,
  __pyx_e_5spacy_7symbols_IS_STOP,
  __pyx_e_5spacy_7symbols_IS_OOV_DEPRECATED,
  __pyx_e_5spacy_7symbols_IS_BRACKET,
  __pyx_e_5spacy_7symbols_IS_QUOTE,
  __pyx_e_5spacy_7symbols_IS_LEFT_PUNCT,
  __pyx_e_5spacy_7symbols_IS_RIGHT_PUNCT,
  __pyx_e_5spacy_7symbols_IS_CURRENCY,
  __pyx_e_5spacy_7symbols_FLAG19 = 19,
  __pyx_e_5spacy_7symbols_FLAG20,
  __pyx_e_5spacy_7symbols_FLAG21,
  __pyx_e_5spacy_7symbols_FLAG22,
  __pyx_e_5spacy_7symbols_FLAG23,
  __pyx_e_5spacy_7symbols_FLAG24,
  __pyx_e_5spacy_7symbols_FLAG25,
  __pyx_e_5spacy_7symbols_FLAG26,
  __pyx_e_5spacy_7symbols_FLAG27,
  __pyx_e_5spacy_7symbols_FLAG28,
  __pyx_e_5spacy_7symbols_FLAG29,
  __pyx_e_5spacy_7symbols_FLAG30,
  __pyx_e_5spacy_7symbols_FLAG31,
  __pyx_e_5spacy_7symbols_FLAG32,
  __pyx_e_5spacy_7symbols_FLAG33,
  __pyx_e_5spacy_7symbols_FLAG34,
  __pyx_e_5spacy_7symbols_FLAG35,
  __pyx_e_5spacy_7symbols_FLAG36,
  __pyx_e_5spacy_7symbols_FLAG37,
  __pyx_e_5spacy_7symbols_FLAG38,
  __pyx_e_5spacy_7symbols_FLAG39,
  __pyx_e_5spacy_7symbols_FLAG40,
  __pyx_e_5spacy_7symbols_FLAG41,
  __pyx_e_5spacy_7symbols_FLAG42,
  __pyx_e_5spacy_7symbols_FLAG43,
  __pyx_e_5spacy_7symbols_FLAG44,
  __pyx_e_5spacy_7symbols_FLAG45,
  __pyx_e_5spacy_7symbols_FLAG46,
  __pyx_e_5spacy_7symbols_FLAG47,
  __pyx_e_5spacy_7symbols_FLAG48,
  __pyx_e_5spacy_7symbols_FLAG49,
  __pyx_e_5spacy_7symbols_FLAG50,
  __pyx_e_5spacy_7symbols_FLAG51,
  __pyx_e_5spacy_7symbols_FLAG52,
  __pyx_e_5spacy_7symbols_FLAG53,
  __pyx_e_5spacy_7symbols_FLAG54,
  __pyx_e_5spacy_7symbols_FLAG55,
  __pyx_e_5spacy_7symbols_FLAG56,
  __pyx_e_5spacy_7symbols_FLAG57,
  __pyx_e_5spacy_7symbols_FLAG58,
  __pyx_e_5spacy_7symbols_FLAG59,
  __pyx_e_5spacy_7symbols_FLAG60,
  __pyx_e_5spacy_7symbols_FLAG61,
  __pyx_e_5spacy_7symbols_FLAG62,
  __pyx_e_5spacy_7symbols_FLAG63,
  __pyx_e_5spacy_7symbols_ID,
  __pyx_e_5spacy_7symbols_ORTH,
  __pyx_e_5spacy_7symbols_LOWER,
  __pyx_e_5spacy_7symbols_NORM,
  __pyx_e_5spacy_7symbols_SHAPE,
  __pyx_e_5spacy_7symbols_PREFIX,
  __pyx_e_5spacy_7symbols_SUFFIX,
  __pyx_e_5spacy_7symbols_LENGTH,
  __pyx_e_5spacy_7symbols_CLUSTER,
  __pyx_e_5spacy_7symbols_LEMMA,
  __pyx_e_5spacy_7symbols_POS,
  __pyx_e_5spacy_7symbols_TAG,
  __pyx_e_5spacy_7symbols_DEP,
  __pyx_e_5spacy_7symbols_ENT_IOB,
  __pyx_e_5spacy_7symbols_ENT_TYPE,
  __pyx_e_5spacy_7symbols_HEAD,
  __pyx_e_5spacy_7symbols_SENT_START,
  __pyx_e_5spacy_7symbols_SPACY,
  __pyx_e_5spacy_7symbols_PROB,
  __pyx_e_5spacy_7symbols_LANG,
  __pyx_e_5spacy_7symbols_ADJ,
  __pyx_e_5spacy_7symbols_ADP,
  __pyx_e_5spacy_7symbols_ADV,
  __pyx_e_5spacy_7symbols_AUX,
  __pyx_e_5spacy_7symbols_CONJ,
  __pyx_e_5spacy_7symbols_CCONJ,
  __pyx_e_5spacy_7symbols_DET,
  __pyx_e_5spacy_7symbols_INTJ,
  __pyx_e_5spacy_7symbols_NOUN,
  __pyx_e_5spacy_7symbols_NUM,
  __pyx_e_5spacy_7symbols_PART,
  __pyx_e_5spacy_7symbols_PRON,
  __pyx_e_5spacy_7symbols_PROPN,
  __pyx_e_5spacy_7symbols_PUNCT,
  __pyx_e_5spacy_7symbols_SCONJ,
  __pyx_e_5spacy_7symbols_SYM,
  __pyx_e_5spacy_7symbols_VERB,
  __pyx_e_5spacy_7symbols_X,
  __pyx_e_5spacy_7symbols_EOL,
  __pyx_e_5spacy_7symbols_SPACE,
  __pyx_e_5spacy_7symbols_DEPRECATED001,
  __pyx_e_5spacy_7symbols_DEPRECATED002,
  __pyx_e_5spacy_7symbols_DEPRECATED003,
  __pyx_e_5spacy_7symbols_DEPRECATED004,
  __pyx_e_5spacy_7symbols_DEPRECATED005,
  __pyx_e_5spacy_7symbols_DEPRECATED006,
  __pyx_e_5spacy_7symbols_DEPRECATED007,
  __pyx_e_5spacy_7symbols_DEPRECATED008,
  __pyx_e_5spacy_7symbols_DEPRECATED009,
  __pyx_e_5spacy_7symbols_DEPRECATED010,
  __pyx_e_5spacy_7symbols_DEPRECATED011,
  __pyx_e_5spacy_7symbols_DEPRECATED012,
  __pyx_e_5spacy_7symbols_DEPRECATED013,
  __pyx_e_5spacy_7symbols_DEPRECATED014,
  __pyx_e_5spacy_7symbols_DEPRECATED015,
  __pyx_e_5spacy_7symbols_DEPRECATED016,
  __pyx_e_5spacy_7symbols_DEPRECATED017,
  __pyx_e_5spacy_7symbols_DEPRECATED018,
  __pyx_e_5spacy_7symbols_DEPRECATED019,
  __pyx_e_5spacy_7symbols_DEPRECATED020,
  __pyx_e_5spacy_7symbols_DEPRECATED021,
  __pyx_e_5spacy_7symbols_DEPRECATED022,
  __pyx_e_5spacy_7symbols_DEPRECATED023,
  __pyx_e_5spacy_7symbols_DEPRECATED024,
  __pyx_e_5spacy_7symbols_DEPRECATED025,
  __pyx_e_5spacy_7symbols_DEPRECATED026,
  __pyx_e_5spacy_7symbols_DEPRECATED027,
  __pyx_e_5spacy_7symbols_DEPRECATED028,
  __pyx_e_5spacy_7symbols_DEPRECATED029,
  __pyx_e_5spacy_7symbols_DEPRECATED030,
  __pyx_e_5spacy_7symbols_DEPRECATED031,
  __pyx_e_5spacy_7symbols_DEPRECATED032,
  __pyx_e_5spacy_7symbols_DEPRECATED033,
  __pyx_e_5spacy_7symbols_DEPRECATED034,
  __pyx_e_5spacy_7symbols_DEPRECATED035,
  __pyx_e_5spacy_7symbols_DEPRECATED036,
  __pyx_e_5spacy_7symbols_DEPRECATED037,
  __pyx_e_5spacy_7symbols_DEPRECATED038,
  __pyx_e_5spacy_7symbols_DEPRECATED039,
  __pyx_e_5spacy_7symbols_DEPRECATED040,
  __pyx_e_5spacy_7symbols_DEPRECATED041,
  __pyx_e_5spacy_7symbols_DEPRECATED042,
  __pyx_e_5spacy_7symbols_DEPRECATED043,
  __pyx_e_5spacy_7symbols_DEPRECATED044,
  __pyx_e_5spacy_7symbols_DEPRECATED045,
  __pyx_e_5spacy_7symbols_DEPRECATED046,
  __pyx_e_5spacy_7symbols_DEPRECATED047,
  __pyx_e_5spacy_7symbols_DEPRECATED048,
  __pyx_e_5spacy_7symbols_DEPRECATED049,
  __pyx_e_5spacy_7symbols_DEPRECATED050,
  __pyx_e_5spacy_7symbols_DEPRECATED051,
  __pyx_e_5spacy_7symbols_DEPRECATED052,
  __pyx_e_5spacy_7symbols_DEPRECATED053,
  __pyx_e_5spacy_7symbols_DEPRECATED054,
  __pyx_e_5spacy_7symbols_DEPRECATED055,
  __pyx_e_5spacy_7symbols_DEPRECATED056,
  __pyx_e_5spacy_7symbols_DEPRECATED057,
  __pyx_e_5spacy_7symbols_DEPRECATED058,
  __pyx_e_5spacy_7symbols_DEPRECATED059,
  __pyx_e_5spacy_7symbols_DEPRECATED060,
  __pyx_e_5spacy_7symbols_DEPRECATED061,
  __pyx_e_5spacy_7symbols_DEPRECATED062,
  __pyx_e_5spacy_7symbols_DEPRECATED063,
  __pyx_e_5spacy_7symbols_DEPRECATED064,
  __pyx_e_5spacy_7symbols_DEPRECATED065,
  __pyx_e_5spacy_7symbols_DEPRECATED066,
  __pyx_e_5spacy_7symbols_DEPRECATED067,
  __pyx_e_5spacy_7symbols_DEPRECATED068,
  __pyx_e_5spacy_7symbols_DEPRECATED069,
  __pyx_e_5spacy_7symbols_DEPRECATED070,
  __pyx_e_5spacy_7symbols_DEPRECATED071,
  __pyx_e_5spacy_7symbols_DEPRECATED072,
  __pyx_e_5spacy_7symbols_DEPRECATED073,
  __pyx_e_5spacy_7symbols_DEPRECATED074,
  __pyx_e_5spacy_7symbols_DEPRECATED075,
  __pyx_e_5spacy_7symbols_DEPRECATED076,
  __pyx_e_5spacy_7symbols_DEPRECATED077,
  __pyx_e_5spacy_7symbols_DEPRECATED078,
  __pyx_e_5spacy_7symbols_DEPRECATED079,
  __pyx_e_5spacy_7symbols_DEPRECATED080,
  __pyx_e_5spacy_7symbols_DEPRECATED081,
  __pyx_e_5spacy_7symbols_DEPRECATED082,
  __pyx_e_5spacy_7symbols_DEPRECATED083,
  __pyx_e_5spacy_7symbols_DEPRECATED084,
  __pyx_e_5spacy_7symbols_DEPRECATED085,
  __pyx_e_5spacy_7symbols_DEPRECATED086,
  __pyx_e_5spacy_7symbols_DEPRECATED087,
  __pyx_e_5spacy_7symbols_DEPRECATED088,
  __pyx_e_5spacy_7symbols_DEPRECATED089,
  __pyx_e_5spacy_7symbols_DEPRECATED090,
  __pyx_e_5spacy_7symbols_DEPRECATED091,
  __pyx_e_5spacy_7symbols_DEPRECATED092,
  __pyx_e_5spacy_7symbols_DEPRECATED093,
  __pyx_e_5spacy_7symbols_DEPRECATED094,
  __pyx_e_5spacy_7symbols_DEPRECATED095,
  __pyx_e_5spacy_7symbols_DEPRECATED096,
  __pyx_e_5spacy_7symbols_DEPRECATED097,
  __pyx_e_5spacy_7symbols_DEPRECATED098,
  __pyx_e_5spacy_7symbols_DEPRECATED099,
  __pyx_e_5spacy_7symbols_DEPRECATED100,
  __pyx_e_5spacy_7symbols_DEPRECATED101,
  __pyx_e_5spacy_7symbols_DEPRECATED102,
  __pyx_e_5spacy_7symbols_DEPRECATED103,
  __pyx_e_5spacy_7symbols_DEPRECATED104,
  __pyx_e_5spacy_7symbols_DEPRECATED105,
  __pyx_e_5spacy_7symbols_DEPRECATED106,
  __pyx_e_5spacy_7symbols_DEPRECATED107,
  __pyx_e_5spacy_7symbols_DEPRECATED108,
  __pyx_e_5spacy_7symbols_DEPRECATED109,
  __pyx_e_5spacy_7symbols_DEPRECATED110,
  __pyx_e_5spacy_7symbols_DEPRECATED111,
  __pyx_e_5spacy_7symbols_DEPRECATED112,
  __pyx_e_5spacy_7symbols_DEPRECATED113,
  __pyx_e_5spacy_7symbols_DEPRECATED114,
  __pyx_e_5spacy_7symbols_DEPRECATED115,
  __pyx_e_5spacy_7symbols_DEPRECATED116,
  __pyx_e_5spacy_7symbols_DEPRECATED117,
  __pyx_e_5spacy_7symbols_DEPRECATED118,
  __pyx_e_5spacy_7symbols_DEPRECATED119,
  __pyx_e_5spacy_7symbols_DEPRECATED120,
  __pyx_e_5spacy_7symbols_DEPRECATED121,
  __pyx_e_5spacy_7symbols_DEPRECATED122,
  __pyx_e_5spacy_7symbols_DEPRECATED123,
  __pyx_e_5spacy_7symbols_DEPRECATED124,
  __pyx_e_5spacy_7symbols_DEPRECATED125,
  __pyx_e_5spacy_7symbols_DEPRECATED126,
  __pyx_e_5spacy_7symbols_DEPRECATED127,
  __pyx_e_5spacy_7symbols_DEPRECATED128,
  __pyx_e_5spacy_7symbols_DEPRECATED129,
  __pyx_e_5spacy_7symbols_DEPRECATED130,
  __pyx_e_5spacy_7symbols_DEPRECATED131,
  __pyx_e_5spacy_7symbols_DEPRECATED132,
  __pyx_e_5spacy_7symbols_DEPRECATED133,
  __pyx_e_5spacy_7symbols_DEPRECATED134,
  __pyx_e_5spacy_7symbols_DEPRECATED135,
  __pyx_e_5spacy_7symbols_DEPRECATED136,
  __pyx_e_5spacy_7symbols_DEPRECATED137,
  __pyx_e_5spacy_7symbols_DEPRECATED138,
  __pyx_e_5spacy_7symbols_DEPRECATED139,
  __pyx_e_5spacy_7symbols_DEPRECATED140,
  __pyx_e_5spacy_7symbols_DEPRECATED141,
  __pyx_e_5spacy_7symbols_DEPRECATED142,
  __pyx_e_5spacy_7symbols_DEPRECATED143,
  __pyx_e_5spacy_7symbols_DEPRECATED144,
  __pyx_e_5spacy_7symbols_DEPRECATED145,
  __pyx_e_5spacy_7symbols_DEPRECATED146,
  __pyx_e_5spacy_7symbols_DEPRECATED147,
  __pyx_e_5spacy_7symbols_DEPRECATED148,
  __pyx_e_5spacy_7symbols_DEPRECATED149,
  __pyx_e_5spacy_7symbols_DEPRECATED150,
  __pyx_e_5spacy_7symbols_DEPRECATED151,
  __pyx_e_5spacy_7symbols_DEPRECATED152,
  __pyx_e_5spacy_7symbols_DEPRECATED153,
  __pyx_e_5spacy_7symbols_DEPRECATED154,
  __pyx_e_5spacy_7symbols_DEPRECATED155,
  __pyx_e_5spacy_7symbols_DEPRECATED156,
  __pyx_e_5spacy_7symbols_DEPRECATED157,
  __pyx_e_5spacy_7symbols_DEPRECATED158,
  __pyx_e_5spacy_7symbols_DEPRECATED159,
  __pyx_e_5spacy_7symbols_DEPRECATED160,
  __pyx_e_5spacy_7symbols_DEPRECATED161,
  __pyx_e_5spacy_7symbols_DEPRECATED162,
  __pyx_e_5spacy_7symbols_DEPRECATED163,
  __pyx_e_5spacy_7symbols_DEPRECATED164,
  __pyx_e_5spacy_7symbols_DEPRECATED165,
  __pyx_e_5spacy_7symbols_DEPRECATED166,
  __pyx_e_5spacy_7symbols_DEPRECATED167,
  __pyx_e_5spacy_7symbols_DEPRECATED168,
  __pyx_e_5spacy_7symbols_DEPRECATED169,
  __pyx_e_5spacy_7symbols_DEPRECATED170,
  __pyx_e_5spacy_7symbols_DEPRECATED171,
  __pyx_e_5spacy_7symbols_DEPRECATED172,
  __pyx_e_5spacy_7symbols_DEPRECATED173,
  __pyx_e_5spacy_7symbols_DEPRECATED174,
  __pyx_e_5spacy_7symbols_DEPRECATED175,
  __pyx_e_5spacy_7symbols_DEPRECATED176,
  __pyx_e_5spacy_7symbols_DEPRECATED177,
  __pyx_e_5spacy_7symbols_DEPRECATED178,
  __pyx_e_5spacy_7symbols_DEPRECATED179,
  __pyx_e_5spacy_7symbols_DEPRECATED180,
  __pyx_e_5spacy_7symbols_DEPRECATED181,
  __pyx_e_5spacy_7symbols_DEPRECATED182,
  __pyx_e_5spacy_7symbols_DEPRECATED183,
  __pyx_e_5spacy_7symbols_DEPRECATED184,
  __pyx_e_5spacy_7symbols_DEPRECATED185,
  __pyx_e_5spacy_7symbols_DEPRECATED186,
  __pyx_e_5spacy_7symbols_DEPRECATED187,
  __pyx_e_5spacy_7symbols_DEPRECATED188,
  __pyx_e_5spacy_7symbols_DEPRECATED189,
  __pyx_e_5spacy_7symbols_DEPRECATED190,
  __pyx_e_5spacy_7symbols_DEPRECATED191,
  __pyx_e_5spacy_7symbols_DEPRECATED192,
  __pyx_e_5spacy_7symbols_DEPRECATED193,
  __pyx_e_5spacy_7symbols_DEPRECATED194,
  __pyx_e_5spacy_7symbols_DEPRECATED195,
  __pyx_e_5spacy_7symbols_DEPRECATED196,
  __pyx_e_5spacy_7symbols_DEPRECATED197,
  __pyx_e_5spacy_7symbols_DEPRECATED198,
  __pyx_e_5spacy_7symbols_DEPRECATED199,
  __pyx_e_5spacy_7symbols_DEPRECATED200,
  __pyx_e_5spacy_7symbols_DEPRECATED201,
  __pyx_e_5spacy_7symbols_DEPRECATED202,
  __pyx_e_5spacy_7symbols_DEPRECATED203,
  __pyx_e_5spacy_7symbols_DEPRECATED204,
  __pyx_e_5spacy_7symbols_DEPRECATED205,
  __pyx_e_5spacy_7symbols_DEPRECATED206,
  __pyx_e_5spacy_7symbols_DEPRECATED207,
  __pyx_e_5spacy_7symbols_DEPRECATED208,
  __pyx_e_5spacy_7symbols_DEPRECATED209,
  __pyx_e_5spacy_7symbols_DEPRECATED210,
  __pyx_e_5spacy_7symbols_DEPRECATED211,
  __pyx_e_5spacy_7symbols_DEPRECATED212,
  __pyx_e_5spacy_7symbols_DEPRECATED213,
  __pyx_e_5spacy_7symbols_DEPRECATED214,
  __pyx_e_5spacy_7symbols_DEPRECATED215,
  __pyx_e_5spacy_7symbols_DEPRECATED216,
  __pyx_e_5spacy_7symbols_DEPRECATED217,
  __pyx_e_5spacy_7symbols_DEPRECATED218,
  __pyx_e_5spacy_7symbols_DEPRECATED219,
  __pyx_e_5spacy_7symbols_DEPRECATED220,
  __pyx_e_5spacy_7symbols_DEPRECATED221,
  __pyx_e_5spacy_7symbols_DEPRECATED222,
  __pyx_e_5spacy_7symbols_DEPRECATED223,
  __pyx_e_5spacy_7symbols_DEPRECATED224,
  __pyx_e_5spacy_7symbols_DEPRECATED225,
  __pyx_e_5spacy_7symbols_DEPRECATED226,
  __pyx_e_5spacy_7symbols_DEPRECATED227,
  __pyx_e_5spacy_7symbols_DEPRECATED228,
  __pyx_e_5spacy_7symbols_DEPRECATED229,
  __pyx_e_5spacy_7symbols_DEPRECATED230,
  __pyx_e_5spacy_7symbols_DEPRECATED231,
  __pyx_e_5spacy_7symbols_DEPRECATED232,
  __pyx_e_5spacy_7symbols_DEPRECATED233,
  __pyx_e_5spacy_7symbols_DEPRECATED234,
  __pyx_e_5spacy_7symbols_DEPRECATED235,
  __pyx_e_5spacy_7symbols_DEPRECATED236,
  __pyx_e_5spacy_7symbols_DEPRECATED237,
  __pyx_e_5spacy_7symbols_DEPRECATED238,
  __pyx_e_5spacy_7symbols_DEPRECATED239,
  __pyx_e_5spacy_7symbols_DEPRECATED240,
  __pyx_e_5spacy_7symbols_DEPRECATED241,
  __pyx_e_5spacy_7symbols_DEPRECATED242,
  __pyx_e_5spacy_7symbols_DEPRECATED243,
  __pyx_e_5spacy_7symbols_DEPRECATED244,
  __pyx_e_5spacy_7symbols_DEPRECATED245,
  __pyx_e_5spacy_7symbols_DEPRECATED246,
  __pyx_e_5spacy_7symbols_DEPRECATED247,
  __pyx_e_5spacy_7symbols_DEPRECATED248,
  __pyx_e_5spacy_7symbols_DEPRECATED249,
  __pyx_e_5spacy_7symbols_DEPRECATED250,
  __pyx_e_5spacy_7symbols_DEPRECATED251,
  __pyx_e_5spacy_7symbols_DEPRECATED252,
  __pyx_e_5spacy_7symbols_DEPRECATED253,
  __pyx_e_5spacy_7symbols_DEPRECATED254,
  __pyx_e_5spacy_7symbols_DEPRECATED255,
  __pyx_e_5spacy_7symbols_DEPRECATED256,
  __pyx_e_5spacy_7symbols_DEPRECATED257,
  __pyx_e_5spacy_7symbols_DEPRECATED258,
  __pyx_e_5spacy_7symbols_DEPRECATED259,
  __pyx_e_5spacy_7symbols_DEPRECATED260,
  __pyx_e_5spacy_7symbols_DEPRECATED261,
  __pyx_e_5spacy_7symbols_DEPRECATED262,
  __pyx_e_5spacy_7symbols_DEPRECATED263,
  __pyx_e_5spacy_7symbols_DEPRECATED264,
  __pyx_e_5spacy_7symbols_DEPRECATED265,
  __pyx_e_5spacy_7symbols_DEPRECATED266,
  __pyx_e_5spacy_7symbols_DEPRECATED267,
  __pyx_e_5spacy_7symbols_DEPRECATED268,
  __pyx_e_5spacy_7symbols_DEPRECATED269,
  __pyx_e_5spacy_7symbols_DEPRECATED270,
  __pyx_e_5spacy_7symbols_DEPRECATED271,
  __pyx_e_5spacy_7symbols_DEPRECATED272,
  __pyx_e_5spacy_7symbols_DEPRECATED273,
  __pyx_e_5spacy_7symbols_DEPRECATED274,
  __pyx_e_5spacy_7symbols_DEPRECATED275,
  __pyx_e_5spacy_7symbols_DEPRECATED276,
  __pyx_e_5spacy_7symbols_PERSON,
  __pyx_e_5spacy_7symbols_NORP,
  __pyx_e_5spacy_7symbols_FACILITY,
  __pyx_e_5spacy_7symbols_ORG,
  __pyx_e_5spacy_7symbols_GPE,
  __pyx_e_5spacy_7symbols_LOC,
  __pyx_e_5spacy_7symbols_PRODUCT,
  __pyx_e_5spacy_7symbols_EVENT,
  __pyx_e_5spacy_7symbols_WORK_OF_ART,
  __pyx_e_5spacy_7symbols_LANGUAGE,
  __pyx_e_5spacy_7symbols_LAW,
  __pyx_e_5spacy_7symbols_DATE,
  __pyx_e_5spacy_7symbols_TIME,
  __pyx_e_5spacy_7symbols_PERCENT,
  __pyx_e_5spacy_7symbols_MONEY,
  __pyx_e_5spacy_7symbols_QUANTITY,
  __pyx_e_5spacy_7symbols_ORDINAL,
  __pyx_e_5spacy_7symbols_CARDINAL,
  __pyx_e_5spacy_7symbols_acomp,
  __pyx_e_5spacy_7symbols_advcl,
  __pyx_e_5spacy_7symbols_advmod,
  __pyx_e_5spacy_7symbols_agent,
  __pyx_e_5spacy_7symbols_amod,
  __pyx_e_5spacy_7symbols_appos,
  __pyx_e_5spacy_7symbols_attr,
  __pyx_e_5spacy_7symbols_aux,
  __pyx_e_5spacy_7symbols_auxpass,
  __pyx_e_5spacy_7symbols_cc,
  __pyx_e_5spacy_7symbols_ccomp,
  __pyx_e_5spacy_7symbols_complm,
  __pyx_e_5spacy_7symbols_conj,
  __pyx_e_5spacy_7symbols_cop,
  __pyx_e_5spacy_7symbols_csubj,
  __pyx_e_5spacy_7symbols_csubjpass,
  __pyx_e_5spacy_7symbols_dep,
  __pyx_e_5spacy_7symbols_det,
  __pyx_e_5spacy_7symbols_dobj,
  __pyx_e_5spacy_7symbols_expl,
  __pyx_e_5spacy_7symbols_hmod,
  __pyx_e_5spacy_7symbols_hyph,
  __pyx_e_5spacy_7symbols_infmod,
  __pyx_e_5spacy_7symbols_intj,
  __pyx_e_5spacy_7symbols_iobj,
  __pyx_e_5spacy_7symbols_mark,
  __pyx_e_5spacy_7symbols_meta,
  __pyx_e_5spacy_7symbols_neg,
  __pyx_e_5spacy_7symbols_nmod,
  __pyx_e_5spacy_7symbols_nn,
  __pyx_e_5spacy_7symbols_npadvmod,
  __pyx_e_5spacy_7symbols_nsubj,
  __pyx_e_5spacy_7symbols_nsubjpass,
  __pyx_e_5spacy_7symbols_num,
  __pyx_e_5spacy_7symbols_number,
  __pyx_e_5spacy_7symbols_oprd,
  __pyx_e_5spacy_7symbols_obj,
  __pyx_e_5spacy_7symbols_obl,
  __pyx_e_5spacy_7symbols_parataxis,
  __pyx_e_5spacy_7symbols_partmod,
  __pyx_e_5spacy_7symbols_pcomp,
  __pyx_e_5spacy_7symbols_pobj,
  __pyx_e_5spacy_7symbols_poss,
  __pyx_e_5spacy_7symbols_possessive,
  __pyx_e_5spacy_7symbols_preconj,
  __pyx_e_5spacy_7symbols_prep,
  __pyx_e_5spacy_7symbols_prt,
  __pyx_e_5spacy_7symbols_punct,
  __pyx_e_5spacy_7symbols_quantmod,
  __pyx_e_5spacy_7symbols_relcl,
  __pyx_e_5spacy_7symbols_rcmod,
  __pyx_e_5spacy_7symbols_root,
  __pyx_e_5spacy_7symbols_xcomp,
  __pyx_e_5spacy_7symbols_acl,
  __pyx_e_5spacy_7symbols_ENT_KB_ID,
  __pyx_e_5spacy_7symbols_MORPH,
  __pyx_e_5spacy_7symbols_ENT_ID,
  __pyx_e_5spacy_7symbols_IDX,
  __pyx_e_5spacy_7symbols__
};

/* "attrs.pxd":5
 * 
 * 
 * cdef enum attr_id_t:             # <<<<<<<<<<<<<<
 *     NULL_ATTR
 *     IS_ALPHA
 */
enum __pyx_t_5spacy_5attrs_attr_id_t {

  /* "attrs.pxd":96
 *     ENT_KB_ID = symbols.ENT_KB_ID
 *     MORPH
 *     ENT_ID = symbols.ENT_ID             # <<<<<<<<<<<<<<
 * 
 *     IDX
 */
  __pyx_e_5spacy_5attrs_NULL_ATTR,
  __pyx_e_5spacy_5attrs_IS_ALPHA,
  __pyx_e_5spacy_5attrs_IS_ASCII,
  __pyx_e_5spacy_5attrs_IS_DIGIT,
  __pyx_e_5spacy_5attrs_IS_LOWER,
  __pyx_e_5spacy_5attrs_IS_PUNCT,
  __pyx_e_5spacy_5attrs_IS_SPACE,
  __pyx_e_5spacy_5attrs_IS_TITLE,
  __pyx_e_5spacy_5attrs_IS_UPPER,
  __pyx_e_5spacy_5attrs_LIKE_URL,
  __pyx_e_5spacy_5attrs_LIKE_NUM,
  __pyx_e_5spacy_5attrs_LIKE_EMAIL,
  __pyx_e_5spacy_5attrs_IS_STOP,
  __pyx_e_5spacy_5attrs_IS_OOV_DEPRECATED,
  __pyx_e_5spacy_5attrs_IS_BRACKET,
  __pyx_e_5spacy_5attrs_IS_QUOTE,
  __pyx_e_5spacy_5attrs_IS_LEFT_PUNCT,
  __pyx_e_5spacy_5attrs_IS_RIGHT_PUNCT,
  __pyx_e_5spacy_5attrs_IS_CURRENCY,
  __pyx_e_5spacy_5attrs_FLAG19 = 19,
  __pyx_e_5spacy_5attrs_FLAG20,
  __pyx_e_5spacy_5attrs_FLAG21,
  __pyx_e_5spacy_5attrs_FLAG22,
  __pyx_e_5spacy_5attrs_FLAG23,
  __pyx_e_5spacy_5attrs_FLAG24,
  __pyx_e_5spacy_5attrs_FLAG25,
  __pyx_e_5spacy_5attrs_FLAG26,
  __pyx_e_5spacy_5attrs_FLAG27,
  __pyx_e_5spacy_5attrs_FLAG28,
  __pyx_e_5spacy_5attrs_FLAG29,
  __pyx_e_5spacy_5attrs_FLAG30,
  __pyx_e_5spacy_5attrs_FLAG31,
  __pyx_e_5spacy_5attrs_FLAG32,
  __pyx_e_5spacy_5attrs_FLAG33,
  __pyx_e_5spacy_5attrs_FLAG34,
  __pyx_e_5spacy_5attrs_FLAG35,
  __pyx_e_5spacy_5attrs_FLAG36,
  __pyx_e_5spacy_5attrs_FLAG37,
  __pyx_e_5spacy_5attrs_FLAG38,
  __pyx_e_5spacy_5attrs_FLAG39,
  __pyx_e_5spacy_5attrs_FLAG40,
  __pyx_e_5spacy_5attrs_FLAG41,
  __pyx_e_5spacy_5attrs_FLAG42,
  __pyx_e_5spacy_5attrs_FLAG43,
  __pyx_e_5spacy_5attrs_FLAG44,
  __pyx_e_5spacy_5attrs_FLAG45,
  __pyx_e_5spacy_5attrs_FLAG46,
  __pyx_e_5spacy_5attrs_FLAG47,
  __pyx_e_5spacy_5attrs_FLAG48,
  __pyx_e_5spacy_5attrs_FLAG49,
  __pyx_e_5spacy_5attrs_FLAG50,
  __pyx_e_5spacy_5attrs_FLAG51,
  __pyx_e_5spacy_5attrs_FLAG52,
  __pyx_e_5spacy_5attrs_FLAG53,
  __pyx_e_5spacy_5attrs_FLAG54,
  __pyx_e_5spacy_5attrs_FLAG55,
  __pyx_e_5spacy_5attrs_FLAG56,
  __pyx_e_5spacy_5attrs_FLAG57,
  __pyx_e_5spacy_5attrs_FLAG58,
  __pyx_e_5spacy_5attrs_FLAG59,
  __pyx_e_5spacy_5attrs_FLAG60,
  __pyx_e_5spacy_5attrs_FLAG61,
  __pyx_e_5spacy_5attrs_FLAG62,
  __pyx_e_5spacy_5attrs_FLAG63,
  __pyx_e_5spacy_5attrs_ID,
  __pyx_e_5spacy_5attrs_ORTH,
  __pyx_e_5spacy_5attrs_LOWER,
  __pyx_e_5spacy_5attrs_NORM,
  __pyx_e_5spacy_5attrs_SHAPE,
  __pyx_e_5spacy_5attrs_PREFIX,
  __pyx_e_5spacy_5attrs_SUFFIX,
  __pyx_e_5spacy_5attrs_LENGTH,
  __pyx_e_5spacy_5attrs_CLUSTER,
  __pyx_e_5spacy_5attrs_LEMMA,
  __pyx_e_5spacy_5attrs_POS,
  __pyx_e_5spacy_5attrs_TAG,
  __pyx_e_5spacy_5attrs_DEP,
  __pyx_e_5spacy_5attrs_ENT_IOB,
  __pyx_e_5spacy_5attrs_ENT_TYPE,
  __pyx_e_5spacy_5attrs_HEAD,
  __pyx_e_5spacy_5attrs_SENT_START,
  __pyx_e_5spacy_5attrs_SPACY,
  __pyx_e_5spacy_5attrs_PROB,
  __pyx_e_5spacy_5attrs_LANG,
  __pyx_e_5spacy_5attrs_ENT_KB_ID = __pyx_e_5spacy_7symbols_ENT_KB_ID,
  __pyx_e_5spacy_5attrs_MORPH,
  __pyx_e_5spacy_5attrs_ENT_ID = __pyx_e_5spacy_7symbols_ENT_ID,
  __pyx_e_5spacy_5attrs_IDX,
  __pyx_e_5spacy_5attrs_SENT_END
};

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":725
 * ctypedef npy_longdouble longdouble_t
 * 
 * ctypedef npy_cfloat      cfloat_t             # <<<<<<<<<<<<<<
 * ctypedef npy_cdouble     cdouble_t
 * ctypedef npy_clongdouble clongdouble_t
 */
typedef npy_cfloat __pyx_t_5numpy_cfloat_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":726
 * 
 * ctypedef npy_cfloat      cfloat_t
 * ctypedef npy_cdouble     cdouble_t             # <<<<<<<<<<<<<<
 * ctypedef npy_clongdouble clongdouble_t
 * 
 */
typedef npy_cdouble __pyx_t_5numpy_cdouble_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":727
 * ctypedef npy_cfloat      cfloat_t
 * ctypedef npy_cdouble     cdouble_t
 * ctypedef npy_clongdouble clongdouble_t             # <<<<<<<<<<<<<<
 * 
 * ctypedef npy_cdouble     complex_t
 */
typedef npy_clongdouble __pyx_t_5numpy_clongdouble_t;

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":729
 * ctypedef npy_clongdouble clongdouble_t
 * 
 * ctypedef npy_cdouble     complex_t             # <<<<<<<<<<<<<<
 * 
 * cdef inline object PyArray_MultiIterNew1(a):
 */
typedef npy_cdouble __pyx_t_5numpy_complex_t;

/* "cymem/cymem.pxd":1
 * ctypedef void* (*malloc_t)(size_t n)             # <<<<<<<<<<<<<<
 * ctypedef void (*free_t)(void *p)
 * 
 */
typedef void *(*__pyx_t_5cymem_5cymem_malloc_t)(size_t);

/* "cymem/cymem.pxd":2
 * ctypedef void* (*malloc_t)(size_t n)
 * ctypedef void (*free_t)(void *p)             # <<<<<<<<<<<<<<
 * 
 * cdef class PyMalloc:
 */
typedef void (*__pyx_t_5cymem_5cymem_free_t)(void *);
struct __pyx_t_7preshed_4maps_Cell;
struct __pyx_t_7preshed_4maps_Result;
struct __pyx_t_7preshed_4maps_MapStruct;

/* "preshed/maps.pxd":8
 * 
 * 
 * cdef struct Cell:             # <<<<<<<<<<<<<<
 *     key_t key
 *     void* value
 */
struct __pyx_t_7preshed_4maps_Cell {
  __pyx_t_7preshed_4maps_key_t key;
  void *value;
};

/* "preshed/maps.pxd":13
 * 
 * 
 * cdef struct Result:             # <<<<<<<<<<<<<<
 *     int found
 *     void* value
 */
struct __pyx_t_7preshed_4maps_Result {
  int found;
  void *value;
};

/* "preshed/maps.pxd":18
 * 
 * 
 * cdef struct MapStruct:             # <<<<<<<<<<<<<<
 *     Cell* cells
 *     void* value_for_empty_key
 */
struct __pyx_t_7preshed_4maps_MapStruct {
  struct __pyx_t_7preshed_4maps_Cell *cells;
  void *value_for_empty_key;
  void *value_for_del_key;
  __pyx_t_7preshed_4maps_key_t length;
  __pyx_t_7preshed_4maps_key_t filled;
  int is_empty_key_set;
  int is_del_key_set;
};
union __pyx_t_5spacy_7strings_Utf8Str;
typedef union __pyx_t_5spacy_7strings_Utf8Str __pyx_t_5spacy_7strings_Utf8Str;

/* "strings.pxd":17
 * 
 * 
 * ctypedef union Utf8Str:             # <<<<<<<<<<<<<<
 *     unsigned char[8] s
 *     unsigned char* p
 */
union __pyx_t_5spacy_7strings_Utf8Str {
  unsigned char s[8];
  unsigned char *p;
};

/* "parts_of_speech.pxd":4
 * 
 * 
 * cpdef enum univ_pos_t:             # <<<<<<<<<<<<<<
 *     NO_TAG = 0
 *     ADJ = symbols.ADJ
 */
enum __pyx_t_5spacy_15parts_of_speech_univ_pos_t {

  /* "parts_of_speech.pxd":6
 * cpdef enum univ_pos_t:
 *     NO_TAG = 0
 *     ADJ = symbols.ADJ             # <<<<<<<<<<<<<<
 *     ADP
 *     ADV
 */
  __pyx_e_5spacy_15parts_of_speech_NO_TAG = 0,
  __pyx_e_5spacy_15parts_of_speech_ADJ = __pyx_e_5spacy_7symbols_ADJ,
  __pyx_e_5spacy_15parts_of_speech_ADP,
  __pyx_e_5spacy_15parts_of_speech_ADV,
  __pyx_e_5spacy_15parts_of_speech_AUX,
  __pyx_e_5spacy_15parts_of_speech_CONJ,
  __pyx_e_5spacy_15parts_of_speech_CCONJ,
  __pyx_e_5spacy_15parts_of_speech_DET,
  __pyx_e_5spacy_15parts_of_speech_INTJ,
  __pyx_e_5spacy_15parts_of_speech_NOUN,
  __pyx_e_5spacy_15parts_of_speech_NUM,
  __pyx_e_5spacy_15parts_of_speech_PART,
  __pyx_e_5spacy_15parts_of_speech_PRON,
  __pyx_e_5spacy_15parts_of_speech_PROPN,
  __pyx_e_5spacy_15parts_of_speech_PUNCT,
  __pyx_e_5spacy_15parts_of_speech_SCONJ,
  __pyx_e_5spacy_15parts_of_speech_SYM,
  __pyx_e_5spacy_15parts_of_speech_VERB,
  __pyx_e_5spacy_15parts_of_speech_X,
  __pyx_e_5spacy_15parts_of_speech_EOL,
  __pyx_e_5spacy_15parts_of_speech_SPACE
};
struct __pyx_t_5spacy_7structs_LexemeC;
struct __pyx_t_5spacy_7structs_SpanC;
struct __pyx_t_5spacy_7structs_TokenC;
struct __pyx_t_5spacy_7structs_MorphAnalysisC;
struct __pyx_t_5spacy_7structs_KBEntryC;
struct __pyx_t_5spacy_7structs_AliasC;
struct __pyx_t_5spacy_7structs_EdgeC;
struct __pyx_t_5spacy_7structs_GraphC;

/* "structs.pxd":10
 * 
 * 
 * cdef struct LexemeC:             # <<<<<<<<<<<<<<
 *     flags_t flags
 * 
 */
struct __pyx_t_5spacy_7structs_LexemeC {
  __pyx_t_5spacy_8typedefs_flags_t flags;
  __pyx_t_5spacy_8typedefs_attr_t lang;
  __pyx_t_5spacy_8typedefs_attr_t id;
  __pyx_t_5spacy_8typedefs_attr_t length;
  __pyx_t_5spacy_8typedefs_attr_t orth;
  __pyx_t_5spacy_8typedefs_attr_t lower;
  __pyx_t_5spacy_8typedefs_attr_t norm;
  __pyx_t_5spacy_8typedefs_attr_t shape;
  __pyx_t_5spacy_8typedefs_attr_t prefix;
  __pyx_t_5spacy_8typedefs_attr_t suffix;
};

/* "structs.pxd":26
 * 
 * 
 * cdef struct SpanC:             # <<<<<<<<<<<<<<
 *     hash_t id
 *     int start
 */
struct __pyx_t_5spacy_7structs_SpanC {
  __pyx_t_5spacy_8typedefs_hash_t id;
  int start;
  int end;
  int start_char;
  int end_char;
  __pyx_t_5spacy_8typedefs_attr_t label;
  __pyx_t_5spacy_8typedefs_attr_t kb_id;
};

/* "structs.pxd":36
 * 
 * 
 * cdef struct TokenC:             # <<<<<<<<<<<<<<
 *     const LexemeC* lex
 *     uint64_t morph
 */
struct __pyx_t_5spacy_7structs_TokenC {
  struct __pyx_t_5spacy_7structs_LexemeC const *lex;
  uint64_t morph;
  enum __pyx_t_5spacy_15parts_of_speech_univ_pos_t pos;
  int spacy;
  __pyx_t_5spacy_8typedefs_attr_t tag;
  int idx;
  __pyx_t_5spacy_8typedefs_attr_t lemma;
  __pyx_t_5spacy_8typedefs_attr_t norm;
  int head;
  __pyx_t_5spacy_8typedefs_attr_t dep;
  uint32_t l_kids;
  uint32_t r_kids;
  uint32_t l_edge;
  uint32_t r_edge;
  int sent_start;
  int ent_iob;
  __pyx_t_5spacy_8typedefs_attr_t ent_type;
  __pyx_t_5spacy_8typedefs_attr_t ent_kb_id;
  __pyx_t_5spacy_8typedefs_hash_t ent_id;
};

/* "structs.pxd":60
 * 
 * 
 * cdef struct MorphAnalysisC:             # <<<<<<<<<<<<<<
 *     hash_t key
 *     int length
 */
struct __pyx_t_5spacy_7structs_MorphAnalysisC {
  __pyx_t_5spacy_8typedefs_hash_t key;
  int length;
  __pyx_t_5spacy_8typedefs_attr_t *fields;
  __pyx_t_5spacy_8typedefs_attr_t *features;
};

/* "structs.pxd":69
 * 
 * # Internal struct, for storage and disambiguation of entities.
 * cdef struct KBEntryC:             # <<<<<<<<<<<<<<
 * 
 *     # The hash of this entry's unique ID/name in the kB
 */
struct __pyx_t_5spacy_7structs_KBEntryC {
  __pyx_t_5spacy_8typedefs_hash_t entity_hash;
  int32_t vector_index;
  int32_t feats_row;
  float freq;
};

/* "structs.pxd":88
 * # Each alias struct stores a list of Entry pointers with their prior probabilities
 * # for this specific mention/alias.
 * cdef struct AliasC:             # <<<<<<<<<<<<<<
 * 
 *     # All entry candidates for this alias
 */
struct __pyx_t_5spacy_7structs_AliasC {
  std::vector<int64_t>  entry_indices;
  std::vector<float>  probs;
};

/* "structs.pxd":97
 * 
 * 
 * cdef struct EdgeC:             # <<<<<<<<<<<<<<
 *     hash_t label
 *     int32_t head
 */
struct __pyx_t_5spacy_7structs_EdgeC {
  __pyx_t_5spacy_8typedefs_hash_t label;
  int32_t head;
  int32_t tail;
};

/* "structs.pxd":103
 * 
 * 
 * cdef struct GraphC:             # <<<<<<<<<<<<<<
 *     vector[vector[int32_t]] nodes
 *     vector[EdgeC] edges
 */
struct __pyx_t_5spacy_7structs_GraphC {
  std::vector<std::vector<int32_t> >  nodes;
  std::vector<struct __pyx_t_5spacy_7structs_EdgeC>  edges;
  std::vector<float>  weights;
  std::vector<int>  n_heads;
  std::vector<int>  n_tails;
  std::vector<int>  first_head;
  std::vector<int>  first_tail;
  std::unordered_set<int>  *roots;
  std::unordered_map<__pyx_t_5spacy_8typedefs_hash_t,int>  *node_map;
  std::unordered_map<__pyx_t_5spacy_8typedefs_hash_t,int>  *edge_map;
};
union __pyx_t_5spacy_5vocab_LexemesOrTokens;
struct __pyx_t_5spacy_5vocab__Cached;

/* "vocab.pxd":15
 * 
 * 
 * cdef union LexemesOrTokens:             # <<<<<<<<<<<<<<
 *     const LexemeC* const* lexemes
 *     const TokenC* tokens
 */
union __pyx_t_5spacy_5vocab_LexemesOrTokens {
  struct __pyx_t_5spacy_7structs_LexemeC const *const *lexemes;
  struct __pyx_t_5spacy_7structs_TokenC const *tokens;
};

/* "vocab.pxd":20
 * 
 * 
 * cdef struct _Cached:             # <<<<<<<<<<<<<<
 *     LexemesOrTokens data
 *     bint is_lex
 */
struct __pyx_t_5spacy_5vocab__Cached {
  union __pyx_t_5spacy_5vocab_LexemesOrTokens data;
  int is_lex;
  int length;
};
struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC;
struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC;

/* "spacy/pipeline/_parser_internals/_state.pxd":23
 *     return Lexeme.c_check_flag(token.lex, IS_SPACE)
 * 
 * cdef struct ArcC:             # <<<<<<<<<<<<<<
 *     int head
 *     int child
 */
struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC {
  int head;
  int child;
  __pyx_t_5spacy_8typedefs_attr_t label;
};
struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC {

  /* "spacy/pipeline/_parser_internals/_state.pxd":29
 * 
 * 
 * cdef cppclass StateC:             # <<<<<<<<<<<<<<
 *     int* _heads
 *     const TokenC* _sent
 */
  int *_heads;
  struct __pyx_t_5spacy_7structs_TokenC const *_sent;
  std::vector<int>  _stack;
  std::vector<int>  _rebuffer;
  std::vector<struct __pyx_t_5spacy_7structs_SpanC>  _ents;
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  _left_arcs;
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  _right_arcs;
  std::vector<bool>  _unshiftable;
  std::set<int>  _sent_starts;
  struct __pyx_t_5spacy_7structs_TokenC _empty_token;
  int length;
  int offset;
  int _b_i;
  void __pyx_f___init__StateC(struct __pyx_t_5spacy_7structs_TokenC const *, int);
  void __pyx_f___dealloc__StateC(void);
  virtual void set_context_tokens(int *, int);
  virtual int S(int) const;
  virtual int B(int) const;
  virtual struct __pyx_t_5spacy_7structs_TokenC const *B_(int) const;
  virtual struct __pyx_t_5spacy_7structs_TokenC const *E_(int) const;
  virtual struct __pyx_t_5spacy_7structs_TokenC const *safe_get(int) const;
  virtual void map_get_arcs(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  const &, std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  *) const;
  virtual void get_arcs(std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  *) const;
  virtual int H(int) const;
  virtual int E(int) const;
  virtual int nth_child(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  const &, int, int) const;
  virtual int L(int, int) const;
  virtual int R(int, int) const;
  virtual int empty(void) const;
  virtual int eol(void) const;
  virtual int is_final(void) const;
  virtual int cannot_sent_start(int) const;
  virtual int is_sent_start(int) const;
  virtual void set_sent_start(int, int);
  virtual int has_head(int) const;
  virtual int l_edge(int) const;
  virtual int r_edge(int) const;
  virtual int n_arcs(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  const &, int) const;
  virtual int n_L(int) const;
  virtual int n_R(int) const;
  virtual int stack_is_connected(void) const;
  virtual int entity_is_open(void) const;
  virtual int stack_depth(void) const;
  virtual int buffer_length(void) const;
  virtual void push(void);
  virtual void pop(void);
  virtual void force_final(void);
  virtual void unshift(void);
  virtual int is_unshiftable(int) const;
  virtual void set_reshiftable(int);
  virtual void add_arc(int, int, __pyx_t_5spacy_8typedefs_attr_t);
  virtual void map_del_arc(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  *, int, int);
  virtual void del_arc(int, int);
  virtual struct __pyx_t_5spacy_7structs_SpanC get_ent(void) const;
  virtual void open_ent(__pyx_t_5spacy_8typedefs_attr_t);
  virtual void close_ent(void);
  virtual void clone(__pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC const *);
  __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC(struct __pyx_t_5spacy_7structs_TokenC const *__pyx_v_sent, int __pyx_v_length) {
    __pyx_f___init__StateC(__pyx_v_sent, __pyx_v_length);
  }
  virtual ~__pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC() {
    __pyx_f___dealloc__StateC();
  }
};

/* "cymem/cymem.pxd":4
 * ctypedef void (*free_t)(void *p)
 * 
 * cdef class PyMalloc:             # <<<<<<<<<<<<<<
 *     cdef malloc_t malloc
 *     cdef void _set(self, malloc_t malloc)
 */
struct __pyx_obj_5cymem_5cymem_PyMalloc {
  PyObject_HEAD
  struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc *__pyx_vtab;
  __pyx_t_5cymem_5cymem_malloc_t malloc;
};


/* "cymem/cymem.pxd":10
 * cdef PyMalloc WrapMalloc(malloc_t malloc)
 * 
 * cdef class PyFree:             # <<<<<<<<<<<<<<
 *     cdef free_t free
 *     cdef void _set(self, free_t free)
 */
struct __pyx_obj_5cymem_5cymem_PyFree {
  PyObject_HEAD
  struct __pyx_vtabstruct_5cymem_5cymem_PyFree *__pyx_vtab;
  __pyx_t_5cymem_5cymem_free_t free;
};


/* "cymem/cymem.pxd":16
 * cdef PyFree WrapFree(free_t free)
 * 
 * cdef class Pool:             # <<<<<<<<<<<<<<
 *     cdef readonly size_t size
 *     cdef readonly dict addresses
 */
struct __pyx_obj_5cymem_5cymem_Pool {
  PyObject_HEAD
  struct __pyx_vtabstruct_5cymem_5cymem_Pool *__pyx_vtab;
  size_t size;
  PyObject *addresses;
  PyObject *refs;
  struct __pyx_obj_5cymem_5cymem_PyMalloc *pymalloc;
  struct __pyx_obj_5cymem_5cymem_PyFree *pyfree;
};


/* "cymem/cymem.pxd":28
 * 
 * 
 * cdef class Address:             # <<<<<<<<<<<<<<
 *     cdef void* ptr
 *     cdef readonly PyMalloc pymalloc
 */
struct __pyx_obj_5cymem_5cymem_Address {
  PyObject_HEAD
  void *ptr;
  struct __pyx_obj_5cymem_5cymem_PyMalloc *pymalloc;
  struct __pyx_obj_5cymem_5cymem_PyFree *pyfree;
};


/* "preshed/maps.pxd":45
 * 
 * 
 * cdef class PreshMap:             # <<<<<<<<<<<<<<
 *     cdef MapStruct* c_map
 *     cdef Pool mem
 */
struct __pyx_obj_7preshed_4maps_PreshMap {
  PyObject_HEAD
  struct __pyx_vtabstruct_7preshed_4maps_PreshMap *__pyx_vtab;
  struct __pyx_t_7preshed_4maps_MapStruct *c_map;
  struct __pyx_obj_5cymem_5cymem_Pool *mem;
};


/* "preshed/maps.pxd":53
 * 
 * 
 * cdef class PreshMapArray:             # <<<<<<<<<<<<<<
 *     cdef Pool mem
 *     cdef MapStruct* maps
 */
struct __pyx_obj_7preshed_4maps_PreshMapArray {
  PyObject_HEAD
  struct __pyx_vtabstruct_7preshed_4maps_PreshMapArray *__pyx_vtab;
  struct __pyx_obj_5cymem_5cymem_Pool *mem;
  struct __pyx_t_7preshed_4maps_MapStruct *maps;
  size_t length;
};


/* "strings.pxd":22
 * 
 * 
 * cdef class StringStore:             # <<<<<<<<<<<<<<
 *     cdef Pool mem
 * 
 */
struct __pyx_obj_5spacy_7strings_StringStore {
  PyObject_HEAD
  struct __pyx_vtabstruct_5spacy_7strings_StringStore *__pyx_vtab;
  struct __pyx_obj_5cymem_5cymem_Pool *mem;
  std::vector<__pyx_t_5spacy_8typedefs_hash_t>  keys;
  struct __pyx_obj_7preshed_4maps_PreshMap *_map;
};


/* "morphology.pxd":11
 * 
 * 
 * cdef class Morphology:             # <<<<<<<<<<<<<<
 *     cdef readonly Pool mem
 *     cdef readonly StringStore strings
 */
struct __pyx_obj_5spacy_10morphology_Morphology {
  PyObject_HEAD
  struct __pyx_vtabstruct_5spacy_10morphology_Morphology *__pyx_vtab;
  struct __pyx_obj_5cymem_5cymem_Pool *mem;
  struct __pyx_obj_5spacy_7strings_StringStore *strings;
  struct __pyx_obj_7preshed_4maps_PreshMap *tags;
};


/* "vocab.pxd":26
 * 
 * 
 * cdef class Vocab:             # <<<<<<<<<<<<<<
 *     cdef Pool mem
 *     cdef readonly StringStore strings
 */
struct __pyx_obj_5spacy_5vocab_Vocab {
  PyObject_HEAD
  struct __pyx_vtabstruct_5spacy_5vocab_Vocab *__pyx_vtab;
  struct __pyx_obj_5cymem_5cymem_Pool *mem;
  struct __pyx_obj_5spacy_7strings_StringStore *strings;
  struct __pyx_obj_5spacy_10morphology_Morphology *morphology;
  PyObject *_vectors;
  PyObject *_lookups;
  PyObject *writing_system;
  PyObject *get_noun_chunks;
  int length;
  PyObject *_unused_object;
  PyObject *lex_attr_getters;
  PyObject *cfg;
  struct __pyx_obj_7preshed_4maps_PreshMap *_by_orth;
};


/* "lexeme.pxd":24
 * cdef attr_t OOV_RANK
 * 
 * cdef class Lexeme:             # <<<<<<<<<<<<<<
 *     cdef LexemeC* c
 *     cdef readonly Vocab vocab
 */
struct __pyx_obj_5spacy_6lexeme_Lexeme {
  PyObject_HEAD
  struct __pyx_vtabstruct_5spacy_6lexeme_Lexeme *__pyx_vtab;
  struct __pyx_t_5spacy_7structs_LexemeC *c;
  struct __pyx_obj_5spacy_5vocab_Vocab *vocab;
  __pyx_t_5spacy_8typedefs_attr_t orth;
};



/* "cymem/cymem.pxd":4
 * ctypedef void (*free_t)(void *p)
 * 
 * cdef class PyMalloc:             # <<<<<<<<<<<<<<
 *     cdef malloc_t malloc
 *     cdef void _set(self, malloc_t malloc)
 */

struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc {
  void (*_set)(struct __pyx_obj_5cymem_5cymem_PyMalloc *, __pyx_t_5cymem_5cymem_malloc_t);
};
static struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc *__pyx_vtabptr_5cymem_5cymem_PyMalloc;


/* "cymem/cymem.pxd":10
 * cdef PyMalloc WrapMalloc(malloc_t malloc)
 * 
 * cdef class PyFree:             # <<<<<<<<<<<<<<
 *     cdef free_t free
 *     cdef void _set(self, free_t free)
 */

struct __pyx_vtabstruct_5cymem_5cymem_PyFree {
  void (*_set)(struct __pyx_obj_5cymem_5cymem_PyFree *, __pyx_t_5cymem_5cymem_free_t);
};
static struct __pyx_vtabstruct_5cymem_5cymem_PyFree *__pyx_vtabptr_5cymem_5cymem_PyFree;


/* "cymem/cymem.pxd":16
 * cdef PyFree WrapFree(free_t free)
 * 
 * cdef class Pool:             # <<<<<<<<<<<<<<
 *     cdef readonly size_t size
 *     cdef readonly dict addresses
 */

struct __pyx_vtabstruct_5cymem_5cymem_Pool {
  void *(*alloc)(struct __pyx_obj_5cymem_5cymem_Pool *, size_t, size_t);
  void (*free)(struct __pyx_obj_5cymem_5cymem_Pool *, void *);
  void *(*realloc)(struct __pyx_obj_5cymem_5cymem_Pool *, void *, size_t);
};
static struct __pyx_vtabstruct_5cymem_5cymem_Pool *__pyx_vtabptr_5cymem_5cymem_Pool;


/* "preshed/maps.pxd":45
 * 
 * 
 * cdef class PreshMap:             # <<<<<<<<<<<<<<
 *     cdef MapStruct* c_map
 *     cdef Pool mem
 */

struct __pyx_vtabstruct_7preshed_4maps_PreshMap {
  void *(*get)(struct __pyx_obj_7preshed_4maps_PreshMap *, __pyx_t_7preshed_4maps_key_t);
  void (*set)(struct __pyx_obj_7preshed_4maps_PreshMap *, __pyx_t_7preshed_4maps_key_t, void *);
};
static struct __pyx_vtabstruct_7preshed_4maps_PreshMap *__pyx_vtabptr_7preshed_4maps_PreshMap;


/* "preshed/maps.pxd":53
 * 
 * 
 * cdef class PreshMapArray:             # <<<<<<<<<<<<<<
 *     cdef Pool mem
 *     cdef MapStruct* maps
 */

struct __pyx_vtabstruct_7preshed_4maps_PreshMapArray {
  void *(*get)(struct __pyx_obj_7preshed_4maps_PreshMapArray *, size_t, __pyx_t_7preshed_4maps_key_t);
  void (*set)(struct __pyx_obj_7preshed_4maps_PreshMapArray *, size_t, __pyx_t_7preshed_4maps_key_t, void *);
};
static struct __pyx_vtabstruct_7preshed_4maps_PreshMapArray *__pyx_vtabptr_7preshed_4maps_PreshMapArray;


/* "strings.pxd":22
 * 
 * 
 * cdef class StringStore:             # <<<<<<<<<<<<<<
 *     cdef Pool mem
 * 
 */

struct __pyx_vtabstruct_5spacy_7strings_StringStore {
  __pyx_t_5spacy_7strings_Utf8Str const *(*intern_unicode)(struct __pyx_obj_5spacy_7strings_StringStore *, PyObject *);
  __pyx_t_5spacy_7strings_Utf8Str const *(*_intern_utf8)(struct __pyx_obj_5spacy_7strings_StringStore *, char *, int, __pyx_t_5spacy_8typedefs_hash_t *);
};
static struct __pyx_vtabstruct_5spacy_7strings_StringStore *__pyx_vtabptr_5spacy_7strings_StringStore;


/* "morphology.pxd":11
 * 
 * 
 * cdef class Morphology:             # <<<<<<<<<<<<<<
 *     cdef readonly Pool mem
 *     cdef readonly StringStore strings
 */

struct __pyx_vtabstruct_5spacy_10morphology_Morphology {
  struct __pyx_t_5spacy_7structs_MorphAnalysisC (*create_morph_tag)(struct __pyx_obj_5spacy_10morphology_Morphology *, PyObject *);
  int (*insert)(struct __pyx_obj_5spacy_10morphology_Morphology *, struct __pyx_t_5spacy_7structs_MorphAnalysisC);
};
static struct __pyx_vtabstruct_5spacy_10morphology_Morphology *__pyx_vtabptr_5spacy_10morphology_Morphology;


/* "vocab.pxd":26
 * 
 * 
 * cdef class Vocab:             # <<<<<<<<<<<<<<
 *     cdef Pool mem
 *     cdef readonly StringStore strings
 */

struct __pyx_vtabstruct_5spacy_5vocab_Vocab {
  struct __pyx_t_5spacy_7structs_LexemeC const *(*get)(struct __pyx_obj_5spacy_5vocab_Vocab *, struct __pyx_obj_5cymem_5cymem_Pool *, PyObject *);
  struct __pyx_t_5spacy_7structs_LexemeC const *(*get_by_orth)(struct __pyx_obj_5spacy_5vocab_Vocab *, struct __pyx_obj_5cymem_5cymem_Pool *, __pyx_t_5spacy_8typedefs_attr_t);
  struct __pyx_t_5spacy_7structs_TokenC const *(*make_fused_token)(struct __pyx_obj_5spacy_5vocab_Vocab *, PyObject *);
  struct __pyx_t_5spacy_7structs_LexemeC const *(*_new_lexeme)(struct __pyx_obj_5spacy_5vocab_Vocab *, struct __pyx_obj_5cymem_5cymem_Pool *, PyObject *);
  int (*_add_lex_to_vocab)(struct __pyx_obj_5spacy_5vocab_Vocab *, __pyx_t_5spacy_8typedefs_hash_t, struct __pyx_t_5spacy_7structs_LexemeC const *);
};
static struct __pyx_vtabstruct_5spacy_5vocab_Vocab *__pyx_vtabptr_5spacy_5vocab_Vocab;


/* "lexeme.pxd":24
 * cdef attr_t OOV_RANK
 * 
 * cdef class Lexeme:             # <<<<<<<<<<<<<<
 *     cdef LexemeC* c
 *     cdef readonly Vocab vocab
 */

struct __pyx_vtabstruct_5spacy_6lexeme_Lexeme {
  struct __pyx_obj_5spacy_6lexeme_Lexeme *(*from_ptr)(struct __pyx_t_5spacy_7structs_LexemeC *, struct __pyx_obj_5spacy_5vocab_Vocab *);
  void (*set_struct_attr)(struct __pyx_t_5spacy_7structs_LexemeC *, enum __pyx_t_5spacy_5attrs_attr_id_t, __pyx_t_5spacy_8typedefs_attr_t);
  __pyx_t_5spacy_8typedefs_attr_t (*get_struct_attr)(struct __pyx_t_5spacy_7structs_LexemeC const *, enum __pyx_t_5spacy_5attrs_attr_id_t);
  int (*c_check_flag)(struct __pyx_t_5spacy_7structs_LexemeC const *, enum __pyx_t_5spacy_5attrs_attr_id_t);
  int (*c_set_flag)(struct __pyx_t_5spacy_7structs_LexemeC *, enum __pyx_t_5spacy_5attrs_attr_id_t, int);
};
static struct __pyx_vtabstruct_5spacy_6lexeme_Lexeme *__pyx_vtabptr_5spacy_6lexeme_Lexeme;
static CYTHON_INLINE struct __pyx_obj_5spacy_6lexeme_Lexeme *__pyx_f_5spacy_6lexeme_6Lexeme_from_ptr(struct __pyx_t_5spacy_7structs_LexemeC *, struct __pyx_obj_5spacy_5vocab_Vocab *);
static CYTHON_INLINE void __pyx_f_5spacy_6lexeme_6Lexeme_set_struct_attr(struct __pyx_t_5spacy_7structs_LexemeC *, enum __pyx_t_5spacy_5attrs_attr_id_t, __pyx_t_5spacy_8typedefs_attr_t);
static CYTHON_INLINE __pyx_t_5spacy_8typedefs_attr_t __pyx_f_5spacy_6lexeme_6Lexeme_get_struct_attr(struct __pyx_t_5spacy_7structs_LexemeC const *, enum __pyx_t_5spacy_5attrs_attr_id_t);
static CYTHON_INLINE int __pyx_f_5spacy_6lexeme_6Lexeme_c_check_flag(struct __pyx_t_5spacy_7structs_LexemeC const *, enum __pyx_t_5spacy_5attrs_attr_id_t);
static CYTHON_INLINE int __pyx_f_5spacy_6lexeme_6Lexeme_c_set_flag(struct __pyx_t_5spacy_7structs_LexemeC *, enum __pyx_t_5spacy_5attrs_attr_id_t, int);

/* --- Runtime support code (head) --- */
/* Refnanny.proto */
#ifndef CYTHON_REFNANNY
  #define CYTHON_REFNANNY 0
#endif
#if CYTHON_REFNANNY
  typedef struct {
    void (*INCREF)(void*, PyObject*, int);
    void (*DECREF)(void*, PyObject*, int);
    void (*GOTREF)(void*, PyObject*, int);
    void (*GIVEREF)(void*, PyObject*, int);
    void* (*SetupContext)(const char*, int, const char*);
    void (*FinishContext)(void**);
  } __Pyx_RefNannyAPIStruct;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNanny = NULL;
  static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname);
  #define __Pyx_RefNannyDeclarations void *__pyx_refnanny = NULL;
#ifdef WITH_THREAD
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          if (acquire_gil) {\
              PyGILState_STATE __pyx_gilstate_save = PyGILState_Ensure();\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
              PyGILState_Release(__pyx_gilstate_save);\
          } else {\
              __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__);\
          }
#else
  #define __Pyx_RefNannySetupContext(name, acquire_gil)\
          __pyx_refnanny = __Pyx_RefNanny->SetupContext((name), __LINE__, __FILE__)
#endif
  #define __Pyx_RefNannyFinishContext()\
          __Pyx_RefNanny->FinishContext(&__pyx_refnanny)
  #define __Pyx_INCREF(r)  __Pyx_RefNanny->INCREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_DECREF(r)  __Pyx_RefNanny->DECREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GOTREF(r)  __Pyx_RefNanny->GOTREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_GIVEREF(r) __Pyx_RefNanny->GIVEREF(__pyx_refnanny, (PyObject *)(r), __LINE__)
  #define __Pyx_XINCREF(r)  do { if((r) != NULL) {__Pyx_INCREF(r); }} while(0)
  #define __Pyx_XDECREF(r)  do { if((r) != NULL) {__Pyx_DECREF(r); }} while(0)
  #define __Pyx_XGOTREF(r)  do { if((r) != NULL) {__Pyx_GOTREF(r); }} while(0)
  #define __Pyx_XGIVEREF(r) do { if((r) != NULL) {__Pyx_GIVEREF(r);}} while(0)
#else
  #define __Pyx_RefNannyDeclarations
  #define __Pyx_RefNannySetupContext(name, acquire_gil)
  #define __Pyx_RefNannyFinishContext()
  #define __Pyx_INCREF(r) Py_INCREF(r)
  #define __Pyx_DECREF(r) Py_DECREF(r)
  #define __Pyx_GOTREF(r)
  #define __Pyx_GIVEREF(r)
  #define __Pyx_XINCREF(r) Py_XINCREF(r)
  #define __Pyx_XDECREF(r) Py_XDECREF(r)
  #define __Pyx_XGOTREF(r)
  #define __Pyx_XGIVEREF(r)
#endif
#define __Pyx_XDECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_XDECREF(tmp);\
    } while (0)
#define __Pyx_DECREF_SET(r, v) do {\
        PyObject *tmp = (PyObject *) r;\
        r = v; __Pyx_DECREF(tmp);\
    } while (0)
#define __Pyx_CLEAR(r)    do { PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);} while(0)
#define __Pyx_XCLEAR(r)   do { if((r) != NULL) {PyObject* tmp = ((PyObject*)(r)); r = NULL; __Pyx_DECREF(tmp);}} while(0)

/* PyObjectGetAttrStr.proto */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name);
#else
#define __Pyx_PyObject_GetAttrStr(o,n) PyObject_GetAttr(o,n)
#endif

/* GetBuiltinName.proto */
static PyObject *__Pyx_GetBuiltinName(PyObject *name);

/* PyThreadStateGet.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyThreadState_declare  PyThreadState *__pyx_tstate;
#define __Pyx_PyThreadState_assign  __pyx_tstate = __Pyx_PyThreadState_Current;
#define __Pyx_PyErr_Occurred()  __pyx_tstate->curexc_type
#else
#define __Pyx_PyThreadState_declare
#define __Pyx_PyThreadState_assign
#define __Pyx_PyErr_Occurred()  PyErr_Occurred()
#endif

/* PyErrFetchRestore.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_Clear() __Pyx_ErrRestore(NULL, NULL, NULL)
#define __Pyx_ErrRestoreWithState(type, value, tb)  __Pyx_ErrRestoreInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)    __Pyx_ErrFetchInState(PyThreadState_GET(), type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  __Pyx_ErrRestoreInState(__pyx_tstate, type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)    __Pyx_ErrFetchInState(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_PyErr_SetNone(exc) (Py_INCREF(exc), __Pyx_ErrRestore((exc), NULL, NULL))
#else
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#endif
#else
#define __Pyx_PyErr_Clear() PyErr_Clear()
#define __Pyx_PyErr_SetNone(exc) PyErr_SetNone(exc)
#define __Pyx_ErrRestoreWithState(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchWithState(type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestoreInState(tstate, type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetchInState(tstate, type, value, tb)  PyErr_Fetch(type, value, tb)
#define __Pyx_ErrRestore(type, value, tb)  PyErr_Restore(type, value, tb)
#define __Pyx_ErrFetch(type, value, tb)  PyErr_Fetch(type, value, tb)
#endif

/* WriteUnraisableException.proto */
static void __Pyx_WriteUnraisable(const char *name, int clineno,
                                  int lineno, const char *filename,
                                  int full_traceback, int nogil);

/* GetTopmostException.proto */
#if CYTHON_USE_EXC_INFO_STACK
static _PyErr_StackItem * __Pyx_PyErr_GetTopmostException(PyThreadState *tstate);
#endif

/* SaveResetException.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_ExceptionSave(type, value, tb)  __Pyx__ExceptionSave(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#define __Pyx_ExceptionReset(type, value, tb)  __Pyx__ExceptionReset(__pyx_tstate, type, value, tb)
static CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb);
#else
#define __Pyx_ExceptionSave(type, value, tb)   PyErr_GetExcInfo(type, value, tb)
#define __Pyx_ExceptionReset(type, value, tb)  PyErr_SetExcInfo(type, value, tb)
#endif

/* PyErrExceptionMatches.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_PyErr_ExceptionMatches(err) __Pyx_PyErr_ExceptionMatchesInState(__pyx_tstate, err)
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err);
#else
#define __Pyx_PyErr_ExceptionMatches(err)  PyErr_ExceptionMatches(err)
#endif

/* GetException.proto */
#if CYTHON_FAST_THREAD_STATE
#define __Pyx_GetException(type, value, tb)  __Pyx__GetException(__pyx_tstate, type, value, tb)
static int __Pyx__GetException(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb);
#else
static int __Pyx_GetException(PyObject **type, PyObject **value, PyObject **tb);
#endif

/* PyObjectCall.proto */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw);
#else
#define __Pyx_PyObject_Call(func, arg, kw) PyObject_Call(func, arg, kw)
#endif

/* RaiseException.proto */
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb, PyObject *cause);

/* tp_new.proto */
#define __Pyx_tp_new(type_obj, args) __Pyx_tp_new_kwargs(type_obj, args, NULL)
static CYTHON_INLINE PyObject* __Pyx_tp_new_kwargs(PyObject* type_obj, PyObject* args, PyObject* kwargs) {
    return (PyObject*) (((PyTypeObject*)type_obj)->tp_new((PyTypeObject*)type_obj, args, kwargs));
}

/* ExtTypeTest.proto */
static CYTHON_INLINE int __Pyx_TypeTest(PyObject *obj, PyTypeObject *type);

/* TypeImport.proto */
#ifndef __PYX_HAVE_RT_ImportType_proto_0_29_36
#define __PYX_HAVE_RT_ImportType_proto_0_29_36
#if __STDC_VERSION__ >= 201112L
#include <stdalign.h>
#endif
#if __STDC_VERSION__ >= 201112L || __cplusplus >= 201103L
#define __PYX_GET_STRUCT_ALIGNMENT_0_29_36(s) alignof(s)
#else
#define __PYX_GET_STRUCT_ALIGNMENT_0_29_36(s) sizeof(void*)
#endif
enum __Pyx_ImportType_CheckSize_0_29_36 {
   __Pyx_ImportType_CheckSize_Error_0_29_36 = 0,
   __Pyx_ImportType_CheckSize_Warn_0_29_36 = 1,
   __Pyx_ImportType_CheckSize_Ignore_0_29_36 = 2
};
static PyTypeObject *__Pyx_ImportType_0_29_36(PyObject* module, const char *module_name, const char *class_name, size_t size, size_t alignment, enum __Pyx_ImportType_CheckSize_0_29_36 check_size);
#endif

/* GetVTable.proto */
static void* __Pyx_GetVtable(PyObject *dict);

/* PyDictVersioning.proto */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
#define __PYX_DICT_VERSION_INIT  ((PY_UINT64_T) -1)
#define __PYX_GET_DICT_VERSION(dict)  (((PyDictObject*)(dict))->ma_version_tag)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)\
    (version_var) = __PYX_GET_DICT_VERSION(dict);\
    (cache_var) = (value);
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP) {\
    static PY_UINT64_T __pyx_dict_version = 0;\
    static PyObject *__pyx_dict_cached_value = NULL;\
    if (likely(__PYX_GET_DICT_VERSION(DICT) == __pyx_dict_version)) {\
        (VAR) = __pyx_dict_cached_value;\
    } else {\
        (VAR) = __pyx_dict_cached_value = (LOOKUP);\
        __pyx_dict_version = __PYX_GET_DICT_VERSION(DICT);\
    }\
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj);
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj);
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version);
#else
#define __PYX_GET_DICT_VERSION(dict)  (0)
#define __PYX_UPDATE_DICT_CACHE(dict, value, cache_var, version_var)
#define __PYX_PY_DICT_LOOKUP_IF_MODIFIED(VAR, DICT, LOOKUP)  (VAR) = (LOOKUP);
#endif

/* CLineInTraceback.proto */
#ifdef CYTHON_CLINE_IN_TRACEBACK
#define __Pyx_CLineForTraceback(tstate, c_line)  (((CYTHON_CLINE_IN_TRACEBACK)) ? c_line : 0)
#else
static int __Pyx_CLineForTraceback(PyThreadState *tstate, int c_line);
#endif

/* CodeObjectCache.proto */
typedef struct {
    PyCodeObject* code_object;
    int code_line;
} __Pyx_CodeObjectCacheEntry;
struct __Pyx_CodeObjectCache {
    int count;
    int max_count;
    __Pyx_CodeObjectCacheEntry* entries;
};
static struct __Pyx_CodeObjectCache __pyx_code_cache = {0,0,NULL};
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line);
static PyCodeObject *__pyx_find_code_object(int code_line);
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object);

/* AddTraceback.proto */
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename);

/* GCCDiagnostics.proto */
#if defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 6))
#define __Pyx_HAS_GCC_DIAGNOSTIC
#endif

/* CppExceptionConversion.proto */
#ifndef __Pyx_CppExn2PyErr
#include <new>
#include <typeinfo>
#include <stdexcept>
#include <ios>
static void __Pyx_CppExn2PyErr() {
  try {
    if (PyErr_Occurred())
      ; // let the latest Python exn pass through and ignore the current one
    else
      throw;
  } catch (const std::bad_alloc& exn) {
    PyErr_SetString(PyExc_MemoryError, exn.what());
  } catch (const std::bad_cast& exn) {
    PyErr_SetString(PyExc_TypeError, exn.what());
  } catch (const std::bad_typeid& exn) {
    PyErr_SetString(PyExc_TypeError, exn.what());
  } catch (const std::domain_error& exn) {
    PyErr_SetString(PyExc_ValueError, exn.what());
  } catch (const std::invalid_argument& exn) {
    PyErr_SetString(PyExc_ValueError, exn.what());
  } catch (const std::ios_base::failure& exn) {
    PyErr_SetString(PyExc_IOError, exn.what());
  } catch (const std::out_of_range& exn) {
    PyErr_SetString(PyExc_IndexError, exn.what());
  } catch (const std::overflow_error& exn) {
    PyErr_SetString(PyExc_OverflowError, exn.what());
  } catch (const std::range_error& exn) {
    PyErr_SetString(PyExc_ArithmeticError, exn.what());
  } catch (const std::underflow_error& exn) {
    PyErr_SetString(PyExc_ArithmeticError, exn.what());
  } catch (const std::exception& exn) {
    PyErr_SetString(PyExc_RuntimeError, exn.what());
  }
  catch (...)
  {
    PyErr_SetString(PyExc_RuntimeError, "Unknown exception");
  }
}
#endif

/* RealImag.proto */
#if CYTHON_CCOMPLEX
  #ifdef __cplusplus
    #define __Pyx_CREAL(z) ((z).real())
    #define __Pyx_CIMAG(z) ((z).imag())
  #else
    #define __Pyx_CREAL(z) (__real__(z))
    #define __Pyx_CIMAG(z) (__imag__(z))
  #endif
#else
    #define __Pyx_CREAL(z) ((z).real)
    #define __Pyx_CIMAG(z) ((z).imag)
#endif
#if defined(__cplusplus) && CYTHON_CCOMPLEX\
        && (defined(_WIN32) || defined(__clang__) || (defined(__GNUC__) && (__GNUC__ >= 5 || __GNUC__ == 4 && __GNUC_MINOR__ >= 4 )) || __cplusplus >= 201103)
    #define __Pyx_SET_CREAL(z,x) ((z).real(x))
    #define __Pyx_SET_CIMAG(z,y) ((z).imag(y))
#else
    #define __Pyx_SET_CREAL(z,x) __Pyx_CREAL(z) = (x)
    #define __Pyx_SET_CIMAG(z,y) __Pyx_CIMAG(z) = (y)
#endif

/* Arithmetic.proto */
#if CYTHON_CCOMPLEX
    #define __Pyx_c_eq_float(a, b)   ((a)==(b))
    #define __Pyx_c_sum_float(a, b)  ((a)+(b))
    #define __Pyx_c_diff_float(a, b) ((a)-(b))
    #define __Pyx_c_prod_float(a, b) ((a)*(b))
    #define __Pyx_c_quot_float(a, b) ((a)/(b))
    #define __Pyx_c_neg_float(a)     (-(a))
  #ifdef __cplusplus
    #define __Pyx_c_is_zero_float(z) ((z)==(float)0)
    #define __Pyx_c_conj_float(z)    (::std::conj(z))
    #if 1
        #define __Pyx_c_abs_float(z)     (::std::abs(z))
        #define __Pyx_c_pow_float(a, b)  (::std::pow(a, b))
    #endif
  #else
    #define __Pyx_c_is_zero_float(z) ((z)==0)
    #define __Pyx_c_conj_float(z)    (conjf(z))
    #if 1
        #define __Pyx_c_abs_float(z)     (cabsf(z))
        #define __Pyx_c_pow_float(a, b)  (cpowf(a, b))
    #endif
 #endif
#else
    static CYTHON_INLINE int __Pyx_c_eq_float(__pyx_t_float_complex, __pyx_t_float_complex);
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_sum_float(__pyx_t_float_complex, __pyx_t_float_complex);
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_diff_float(__pyx_t_float_complex, __pyx_t_float_complex);
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_prod_float(__pyx_t_float_complex, __pyx_t_float_complex);
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_quot_float(__pyx_t_float_complex, __pyx_t_float_complex);
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_neg_float(__pyx_t_float_complex);
    static CYTHON_INLINE int __Pyx_c_is_zero_float(__pyx_t_float_complex);
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_conj_float(__pyx_t_float_complex);
    #if 1
        static CYTHON_INLINE float __Pyx_c_abs_float(__pyx_t_float_complex);
        static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_pow_float(__pyx_t_float_complex, __pyx_t_float_complex);
    #endif
#endif

/* Arithmetic.proto */
#if CYTHON_CCOMPLEX
    #define __Pyx_c_eq_double(a, b)   ((a)==(b))
    #define __Pyx_c_sum_double(a, b)  ((a)+(b))
    #define __Pyx_c_diff_double(a, b) ((a)-(b))
    #define __Pyx_c_prod_double(a, b) ((a)*(b))
    #define __Pyx_c_quot_double(a, b) ((a)/(b))
    #define __Pyx_c_neg_double(a)     (-(a))
  #ifdef __cplusplus
    #define __Pyx_c_is_zero_double(z) ((z)==(double)0)
    #define __Pyx_c_conj_double(z)    (::std::conj(z))
    #if 1
        #define __Pyx_c_abs_double(z)     (::std::abs(z))
        #define __Pyx_c_pow_double(a, b)  (::std::pow(a, b))
    #endif
  #else
    #define __Pyx_c_is_zero_double(z) ((z)==0)
    #define __Pyx_c_conj_double(z)    (conj(z))
    #if 1
        #define __Pyx_c_abs_double(z)     (cabs(z))
        #define __Pyx_c_pow_double(a, b)  (cpow(a, b))
    #endif
 #endif
#else
    static CYTHON_INLINE int __Pyx_c_eq_double(__pyx_t_double_complex, __pyx_t_double_complex);
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_sum_double(__pyx_t_double_complex, __pyx_t_double_complex);
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_diff_double(__pyx_t_double_complex, __pyx_t_double_complex);
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_prod_double(__pyx_t_double_complex, __pyx_t_double_complex);
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_quot_double(__pyx_t_double_complex, __pyx_t_double_complex);
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_neg_double(__pyx_t_double_complex);
    static CYTHON_INLINE int __Pyx_c_is_zero_double(__pyx_t_double_complex);
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_conj_double(__pyx_t_double_complex);
    #if 1
        static CYTHON_INLINE double __Pyx_c_abs_double(__pyx_t_double_complex);
        static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_pow_double(__pyx_t_double_complex, __pyx_t_double_complex);
    #endif
#endif

/* None.proto */
#include <new>

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int(int value);

/* CIntFromPy.proto */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *);

/* CIntFromPy.proto */
static CYTHON_INLINE size_t __Pyx_PyInt_As_size_t(PyObject *);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_uint64_t(uint64_t value);

/* CIntToPy.proto */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value);

/* CIntFromPy.proto */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *);

/* FastTypeChecks.proto */
#if CYTHON_COMPILING_IN_CPYTHON
#define __Pyx_TypeCheck(obj, type) __Pyx_IsSubtype(Py_TYPE(obj), (PyTypeObject *)type)
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject *type);
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *type1, PyObject *type2);
#else
#define __Pyx_TypeCheck(obj, type) PyObject_TypeCheck(obj, (PyTypeObject *)type)
#define __Pyx_PyErr_GivenExceptionMatches(err, type) PyErr_GivenExceptionMatches(err, type)
#define __Pyx_PyErr_GivenExceptionMatches2(err, type1, type2) (PyErr_GivenExceptionMatches(err, type1) || PyErr_GivenExceptionMatches(err, type2))
#endif
#define __Pyx_PyException_Check(obj) __Pyx_TypeCheck(obj, PyExc_Exception)

/* CheckBinaryVersion.proto */
static int __Pyx_check_binary_version(void);

/* VoidPtrImport.proto */
static int __Pyx_ImportVoidPtr_0_29_36(PyObject *module, const char *name, void **p, const char *sig);

/* FunctionImport.proto */
static int __Pyx_ImportFunction_0_29_36(PyObject *module, const char *funcname, void (**f)(void), const char *sig);

/* InitStrings.proto */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t);

static CYTHON_INLINE struct __pyx_obj_5spacy_6lexeme_Lexeme *__pyx_f_5spacy_6lexeme_6Lexeme_from_ptr(struct __pyx_t_5spacy_7structs_LexemeC *__pyx_v_lex, struct __pyx_obj_5spacy_5vocab_Vocab *__pyx_v_vocab); /* proto*/
static CYTHON_INLINE void __pyx_f_5spacy_6lexeme_6Lexeme_set_struct_attr(struct __pyx_t_5spacy_7structs_LexemeC *__pyx_v_lex, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_name, __pyx_t_5spacy_8typedefs_attr_t __pyx_v_value); /* proto*/
static CYTHON_INLINE __pyx_t_5spacy_8typedefs_attr_t __pyx_f_5spacy_6lexeme_6Lexeme_get_struct_attr(struct __pyx_t_5spacy_7structs_LexemeC const *__pyx_v_lex, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_feat_name); /* proto*/
static CYTHON_INLINE int __pyx_f_5spacy_6lexeme_6Lexeme_c_check_flag(struct __pyx_t_5spacy_7structs_LexemeC const *__pyx_v_lexeme, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_flag_id); /* proto*/
static CYTHON_INLINE int __pyx_f_5spacy_6lexeme_6Lexeme_c_set_flag(struct __pyx_t_5spacy_7structs_LexemeC *__pyx_v_lex, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_flag_id, int __pyx_v_value); /* proto*/

/* Module declarations from 'libcpp' */

/* Module declarations from 'libc.string' */

/* Module declarations from 'libc.stdio' */

/* Module declarations from '__builtin__' */

/* Module declarations from 'cpython.type' */
static PyTypeObject *__pyx_ptype_7cpython_4type_type = 0;

/* Module declarations from 'cpython.buffer' */

/* Module declarations from 'cpython' */

/* Module declarations from 'cpython.object' */

/* Module declarations from 'cpython.exc' */

/* Module declarations from 'libc.stdint' */

/* Module declarations from 'libc.stdlib' */

/* Module declarations from 'libcpp.utility' */

/* Module declarations from 'libcpp.set' */

/* Module declarations from 'libcpp.unordered_map' */

/* Module declarations from 'libcpp.vector' */

/* Module declarations from 'murmurhash.mrmr' */
static uint64_t (*__pyx_f_10murmurhash_4mrmr_hash64)(void *, int, uint64_t); /*proto*/

/* Module declarations from 'spacy' */

/* Module declarations from 'spacy.symbols' */

/* Module declarations from 'spacy.attrs' */

/* Module declarations from 'cpython.ref' */

/* Module declarations from 'cpython.mem' */

/* Module declarations from 'numpy' */

/* Module declarations from 'numpy' */
static PyTypeObject *__pyx_ptype_5numpy_dtype = 0;
static PyTypeObject *__pyx_ptype_5numpy_flatiter = 0;
static PyTypeObject *__pyx_ptype_5numpy_broadcast = 0;
static PyTypeObject *__pyx_ptype_5numpy_ndarray = 0;
static PyTypeObject *__pyx_ptype_5numpy_generic = 0;
static PyTypeObject *__pyx_ptype_5numpy_number = 0;
static PyTypeObject *__pyx_ptype_5numpy_integer = 0;
static PyTypeObject *__pyx_ptype_5numpy_signedinteger = 0;
static PyTypeObject *__pyx_ptype_5numpy_unsignedinteger = 0;
static PyTypeObject *__pyx_ptype_5numpy_inexact = 0;
static PyTypeObject *__pyx_ptype_5numpy_floating = 0;
static PyTypeObject *__pyx_ptype_5numpy_complexfloating = 0;
static PyTypeObject *__pyx_ptype_5numpy_flexible = 0;
static PyTypeObject *__pyx_ptype_5numpy_character = 0;
static PyTypeObject *__pyx_ptype_5numpy_ufunc = 0;

/* Module declarations from 'cymem.cymem' */
static PyTypeObject *__pyx_ptype_5cymem_5cymem_PyMalloc = 0;
static PyTypeObject *__pyx_ptype_5cymem_5cymem_PyFree = 0;
static PyTypeObject *__pyx_ptype_5cymem_5cymem_Pool = 0;
static PyTypeObject *__pyx_ptype_5cymem_5cymem_Address = 0;

/* Module declarations from 'preshed.maps' */
static PyTypeObject *__pyx_ptype_7preshed_4maps_PreshMap = 0;
static PyTypeObject *__pyx_ptype_7preshed_4maps_PreshMapArray = 0;

/* Module declarations from 'spacy.typedefs' */

/* Module declarations from 'spacy.strings' */
static PyTypeObject *__pyx_ptype_5spacy_7strings_StringStore = 0;

/* Module declarations from 'libcpp.unordered_set' */

/* Module declarations from 'spacy.parts_of_speech' */

/* Module declarations from 'spacy.structs' */

/* Module declarations from 'spacy.morphology' */
static PyTypeObject *__pyx_ptype_5spacy_10morphology_Morphology = 0;

/* Module declarations from 'spacy.vocab' */
static PyTypeObject *__pyx_ptype_5spacy_5vocab_Vocab = 0;
static struct __pyx_t_5spacy_7structs_LexemeC *__pyx_vp_5spacy_5vocab_EMPTY_LEXEME = 0;
#define __pyx_v_5spacy_5vocab_EMPTY_LEXEME (*__pyx_vp_5spacy_5vocab_EMPTY_LEXEME)

/* Module declarations from 'spacy.lexeme' */
static PyTypeObject *__pyx_ptype_5spacy_6lexeme_Lexeme = 0;
static struct __pyx_t_5spacy_7structs_LexemeC *__pyx_vp_5spacy_6lexeme_EMPTY_LEXEME = 0;
#define __pyx_v_5spacy_6lexeme_EMPTY_LEXEME (*__pyx_vp_5spacy_6lexeme_EMPTY_LEXEME)
static __pyx_t_5spacy_8typedefs_attr_t *__pyx_vp_5spacy_6lexeme_OOV_RANK = 0;
#define __pyx_v_5spacy_6lexeme_OOV_RANK (*__pyx_vp_5spacy_6lexeme_OOV_RANK)

/* Module declarations from 'spacy.pipeline._parser_internals._state' */
#define __Pyx_MODULE_NAME "spacy.pipeline._parser_internals._state"
extern int __pyx_module_is_main_spacy__pipeline___parser_internals___state;
int __pyx_module_is_main_spacy__pipeline___parser_internals___state = 0;

/* Implementation of 'spacy.pipeline._parser_internals._state' */
static PyObject *__pyx_builtin_MemoryError;
static PyObject *__pyx_builtin_range;
static PyObject *__pyx_builtin_ImportError;
static const char __pyx_k_main[] = "__main__";
static const char __pyx_k_name[] = "__name__";
static const char __pyx_k_test[] = "__test__";
static const char __pyx_k_range[] = "range";
static const char __pyx_k_pyx_vtable[] = "__pyx_vtable__";
static const char __pyx_k_ImportError[] = "ImportError";
static const char __pyx_k_MemoryError[] = "MemoryError";
static const char __pyx_k_cline_in_traceback[] = "cline_in_traceback";
static const char __pyx_k_numpy_core_multiarray_failed_to[] = "numpy.core.multiarray failed to import";
static const char __pyx_k_numpy_core_umath_failed_to_impor[] = "numpy.core.umath failed to import";
static PyObject *__pyx_n_s_ImportError;
static PyObject *__pyx_n_s_MemoryError;
static PyObject *__pyx_n_s_cline_in_traceback;
static PyObject *__pyx_n_s_main;
static PyObject *__pyx_n_s_name;
static PyObject *__pyx_kp_s_numpy_core_multiarray_failed_to;
static PyObject *__pyx_kp_s_numpy_core_umath_failed_to_impor;
static PyObject *__pyx_n_s_pyx_vtable;
static PyObject *__pyx_n_s_range;
static PyObject *__pyx_n_s_test;
static PyObject *__pyx_tuple_;
static PyObject *__pyx_tuple__2;
/* Late includes */

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":731
 * ctypedef npy_cdouble     complex_t
 * 
 * cdef inline object PyArray_MultiIterNew1(a):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(1, <void*>a)
 * 
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_PyArray_MultiIterNew1(PyObject *__pyx_v_a) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("PyArray_MultiIterNew1", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":732
 * 
 * cdef inline object PyArray_MultiIterNew1(a):
 *     return PyArray_MultiIterNew(1, <void*>a)             # <<<<<<<<<<<<<<
 * 
 * cdef inline object PyArray_MultiIterNew2(a, b):
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = PyArray_MultiIterNew(1, ((void *)__pyx_v_a)); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 732, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":731
 * ctypedef npy_cdouble     complex_t
 * 
 * cdef inline object PyArray_MultiIterNew1(a):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(1, <void*>a)
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("numpy.PyArray_MultiIterNew1", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":734
 *     return PyArray_MultiIterNew(1, <void*>a)
 * 
 * cdef inline object PyArray_MultiIterNew2(a, b):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(2, <void*>a, <void*>b)
 * 
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_PyArray_MultiIterNew2(PyObject *__pyx_v_a, PyObject *__pyx_v_b) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("PyArray_MultiIterNew2", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":735
 * 
 * cdef inline object PyArray_MultiIterNew2(a, b):
 *     return PyArray_MultiIterNew(2, <void*>a, <void*>b)             # <<<<<<<<<<<<<<
 * 
 * cdef inline object PyArray_MultiIterNew3(a, b, c):
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = PyArray_MultiIterNew(2, ((void *)__pyx_v_a), ((void *)__pyx_v_b)); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 735, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":734
 *     return PyArray_MultiIterNew(1, <void*>a)
 * 
 * cdef inline object PyArray_MultiIterNew2(a, b):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(2, <void*>a, <void*>b)
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("numpy.PyArray_MultiIterNew2", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":737
 *     return PyArray_MultiIterNew(2, <void*>a, <void*>b)
 * 
 * cdef inline object PyArray_MultiIterNew3(a, b, c):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(3, <void*>a, <void*>b, <void*> c)
 * 
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_PyArray_MultiIterNew3(PyObject *__pyx_v_a, PyObject *__pyx_v_b, PyObject *__pyx_v_c) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("PyArray_MultiIterNew3", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":738
 * 
 * cdef inline object PyArray_MultiIterNew3(a, b, c):
 *     return PyArray_MultiIterNew(3, <void*>a, <void*>b, <void*> c)             # <<<<<<<<<<<<<<
 * 
 * cdef inline object PyArray_MultiIterNew4(a, b, c, d):
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = PyArray_MultiIterNew(3, ((void *)__pyx_v_a), ((void *)__pyx_v_b), ((void *)__pyx_v_c)); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 738, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":737
 *     return PyArray_MultiIterNew(2, <void*>a, <void*>b)
 * 
 * cdef inline object PyArray_MultiIterNew3(a, b, c):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(3, <void*>a, <void*>b, <void*> c)
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("numpy.PyArray_MultiIterNew3", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":740
 *     return PyArray_MultiIterNew(3, <void*>a, <void*>b, <void*> c)
 * 
 * cdef inline object PyArray_MultiIterNew4(a, b, c, d):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(4, <void*>a, <void*>b, <void*>c, <void*> d)
 * 
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_PyArray_MultiIterNew4(PyObject *__pyx_v_a, PyObject *__pyx_v_b, PyObject *__pyx_v_c, PyObject *__pyx_v_d) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("PyArray_MultiIterNew4", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":741
 * 
 * cdef inline object PyArray_MultiIterNew4(a, b, c, d):
 *     return PyArray_MultiIterNew(4, <void*>a, <void*>b, <void*>c, <void*> d)             # <<<<<<<<<<<<<<
 * 
 * cdef inline object PyArray_MultiIterNew5(a, b, c, d, e):
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = PyArray_MultiIterNew(4, ((void *)__pyx_v_a), ((void *)__pyx_v_b), ((void *)__pyx_v_c), ((void *)__pyx_v_d)); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 741, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":740
 *     return PyArray_MultiIterNew(3, <void*>a, <void*>b, <void*> c)
 * 
 * cdef inline object PyArray_MultiIterNew4(a, b, c, d):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(4, <void*>a, <void*>b, <void*>c, <void*> d)
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("numpy.PyArray_MultiIterNew4", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":743
 *     return PyArray_MultiIterNew(4, <void*>a, <void*>b, <void*>c, <void*> d)
 * 
 * cdef inline object PyArray_MultiIterNew5(a, b, c, d, e):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(5, <void*>a, <void*>b, <void*>c, <void*> d, <void*> e)
 * 
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_PyArray_MultiIterNew5(PyObject *__pyx_v_a, PyObject *__pyx_v_b, PyObject *__pyx_v_c, PyObject *__pyx_v_d, PyObject *__pyx_v_e) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("PyArray_MultiIterNew5", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":744
 * 
 * cdef inline object PyArray_MultiIterNew5(a, b, c, d, e):
 *     return PyArray_MultiIterNew(5, <void*>a, <void*>b, <void*>c, <void*> d, <void*> e)             # <<<<<<<<<<<<<<
 * 
 * cdef inline tuple PyDataType_SHAPE(dtype d):
 */
  __Pyx_XDECREF(__pyx_r);
  __pyx_t_1 = PyArray_MultiIterNew(5, ((void *)__pyx_v_a), ((void *)__pyx_v_b), ((void *)__pyx_v_c), ((void *)__pyx_v_d), ((void *)__pyx_v_e)); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 744, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_r = __pyx_t_1;
  __pyx_t_1 = 0;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":743
 *     return PyArray_MultiIterNew(4, <void*>a, <void*>b, <void*>c, <void*> d)
 * 
 * cdef inline object PyArray_MultiIterNew5(a, b, c, d, e):             # <<<<<<<<<<<<<<
 *     return PyArray_MultiIterNew(5, <void*>a, <void*>b, <void*>c, <void*> d, <void*> e)
 * 
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_AddTraceback("numpy.PyArray_MultiIterNew5", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":746
 *     return PyArray_MultiIterNew(5, <void*>a, <void*>b, <void*>c, <void*> d, <void*> e)
 * 
 * cdef inline tuple PyDataType_SHAPE(dtype d):             # <<<<<<<<<<<<<<
 *     if PyDataType_HASSUBARRAY(d):
 *         return <tuple>d.subarray.shape
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_PyDataType_SHAPE(PyArray_Descr *__pyx_v_d) {
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("PyDataType_SHAPE", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":747
 * 
 * cdef inline tuple PyDataType_SHAPE(dtype d):
 *     if PyDataType_HASSUBARRAY(d):             # <<<<<<<<<<<<<<
 *         return <tuple>d.subarray.shape
 *     else:
 */
  __pyx_t_1 = (PyDataType_HASSUBARRAY(__pyx_v_d) != 0);
  if (__pyx_t_1) {

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":748
 * cdef inline tuple PyDataType_SHAPE(dtype d):
 *     if PyDataType_HASSUBARRAY(d):
 *         return <tuple>d.subarray.shape             # <<<<<<<<<<<<<<
 *     else:
 *         return ()
 */
    __Pyx_XDECREF(__pyx_r);
    __Pyx_INCREF(((PyObject*)__pyx_v_d->subarray->shape));
    __pyx_r = ((PyObject*)__pyx_v_d->subarray->shape);
    goto __pyx_L0;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":747
 * 
 * cdef inline tuple PyDataType_SHAPE(dtype d):
 *     if PyDataType_HASSUBARRAY(d):             # <<<<<<<<<<<<<<
 *         return <tuple>d.subarray.shape
 *     else:
 */
  }

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":750
 *         return <tuple>d.subarray.shape
 *     else:
 *         return ()             # <<<<<<<<<<<<<<
 * 
 * 
 */
  /*else*/ {
    __Pyx_XDECREF(__pyx_r);
    __Pyx_INCREF(__pyx_empty_tuple);
    __pyx_r = __pyx_empty_tuple;
    goto __pyx_L0;
  }

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":746
 *     return PyArray_MultiIterNew(5, <void*>a, <void*>b, <void*>c, <void*> d, <void*> e)
 * 
 * cdef inline tuple PyDataType_SHAPE(dtype d):             # <<<<<<<<<<<<<<
 *     if PyDataType_HASSUBARRAY(d):
 *         return <tuple>d.subarray.shape
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":925
 *     int _import_umath() except -1
 * 
 * cdef inline void set_array_base(ndarray arr, object base):             # <<<<<<<<<<<<<<
 *     Py_INCREF(base) # important to do this before stealing the reference below!
 *     PyArray_SetBaseObject(arr, base)
 */

static CYTHON_INLINE void __pyx_f_5numpy_set_array_base(PyArrayObject *__pyx_v_arr, PyObject *__pyx_v_base) {
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("set_array_base", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":926
 * 
 * cdef inline void set_array_base(ndarray arr, object base):
 *     Py_INCREF(base) # important to do this before stealing the reference below!             # <<<<<<<<<<<<<<
 *     PyArray_SetBaseObject(arr, base)
 * 
 */
  Py_INCREF(__pyx_v_base);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":927
 * cdef inline void set_array_base(ndarray arr, object base):
 *     Py_INCREF(base) # important to do this before stealing the reference below!
 *     PyArray_SetBaseObject(arr, base)             # <<<<<<<<<<<<<<
 * 
 * cdef inline object get_array_base(ndarray arr):
 */
  __pyx_t_1 = PyArray_SetBaseObject(__pyx_v_arr, __pyx_v_base); if (unlikely(__pyx_t_1 == ((int)-1))) __PYX_ERR(1, 927, __pyx_L1_error)

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":925
 *     int _import_umath() except -1
 * 
 * cdef inline void set_array_base(ndarray arr, object base):             # <<<<<<<<<<<<<<
 *     Py_INCREF(base) # important to do this before stealing the reference below!
 *     PyArray_SetBaseObject(arr, base)
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("numpy.set_array_base", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 0);
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":929
 *     PyArray_SetBaseObject(arr, base)
 * 
 * cdef inline object get_array_base(ndarray arr):             # <<<<<<<<<<<<<<
 *     base = PyArray_BASE(arr)
 *     if base is NULL:
 */

static CYTHON_INLINE PyObject *__pyx_f_5numpy_get_array_base(PyArrayObject *__pyx_v_arr) {
  PyObject *__pyx_v_base;
  PyObject *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  __Pyx_RefNannySetupContext("get_array_base", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":930
 * 
 * cdef inline object get_array_base(ndarray arr):
 *     base = PyArray_BASE(arr)             # <<<<<<<<<<<<<<
 *     if base is NULL:
 *         return None
 */
  __pyx_v_base = PyArray_BASE(__pyx_v_arr);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":931
 * cdef inline object get_array_base(ndarray arr):
 *     base = PyArray_BASE(arr)
 *     if base is NULL:             # <<<<<<<<<<<<<<
 *         return None
 *     return <object>base
 */
  __pyx_t_1 = ((__pyx_v_base == NULL) != 0);
  if (__pyx_t_1) {

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":932
 *     base = PyArray_BASE(arr)
 *     if base is NULL:
 *         return None             # <<<<<<<<<<<<<<
 *     return <object>base
 * 
 */
    __Pyx_XDECREF(__pyx_r);
    __pyx_r = Py_None; __Pyx_INCREF(Py_None);
    goto __pyx_L0;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":931
 * cdef inline object get_array_base(ndarray arr):
 *     base = PyArray_BASE(arr)
 *     if base is NULL:             # <<<<<<<<<<<<<<
 *         return None
 *     return <object>base
 */
  }

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":933
 *     if base is NULL:
 *         return None
 *     return <object>base             # <<<<<<<<<<<<<<
 * 
 * # Versions of the import_* functions which are more suitable for
 */
  __Pyx_XDECREF(__pyx_r);
  __Pyx_INCREF(((PyObject *)__pyx_v_base));
  __pyx_r = ((PyObject *)__pyx_v_base);
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":929
 *     PyArray_SetBaseObject(arr, base)
 * 
 * cdef inline object get_array_base(ndarray arr):             # <<<<<<<<<<<<<<
 *     base = PyArray_BASE(arr)
 *     if base is NULL:
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_XGIVEREF(__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":937
 * # Versions of the import_* functions which are more suitable for
 * # Cython code.
 * cdef inline int import_array() except -1:             # <<<<<<<<<<<<<<
 *     try:
 *         __pyx_import_array()
 */

static CYTHON_INLINE int __pyx_f_5numpy_import_array(void) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  int __pyx_t_4;
  PyObject *__pyx_t_5 = NULL;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("import_array", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":938
 * # Cython code.
 * cdef inline int import_array() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         __pyx_import_array()
 *     except Exception:
 */
  {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ExceptionSave(&__pyx_t_1, &__pyx_t_2, &__pyx_t_3);
    __Pyx_XGOTREF(__pyx_t_1);
    __Pyx_XGOTREF(__pyx_t_2);
    __Pyx_XGOTREF(__pyx_t_3);
    /*try:*/ {

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":939
 * cdef inline int import_array() except -1:
 *     try:
 *         __pyx_import_array()             # <<<<<<<<<<<<<<
 *     except Exception:
 *         raise ImportError("numpy.core.multiarray failed to import")
 */
      __pyx_t_4 = _import_array(); if (unlikely(__pyx_t_4 == ((int)-1))) __PYX_ERR(1, 939, __pyx_L3_error)

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":938
 * # Cython code.
 * cdef inline int import_array() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         __pyx_import_array()
 *     except Exception:
 */
    }
    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
    __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
    goto __pyx_L8_try_end;
    __pyx_L3_error:;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":940
 *     try:
 *         __pyx_import_array()
 *     except Exception:             # <<<<<<<<<<<<<<
 *         raise ImportError("numpy.core.multiarray failed to import")
 * 
 */
    __pyx_t_4 = __Pyx_PyErr_ExceptionMatches(((PyObject *)(&((PyTypeObject*)PyExc_Exception)[0])));
    if (__pyx_t_4) {
      __Pyx_AddTraceback("numpy.import_array", __pyx_clineno, __pyx_lineno, __pyx_filename);
      if (__Pyx_GetException(&__pyx_t_5, &__pyx_t_6, &__pyx_t_7) < 0) __PYX_ERR(1, 940, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_5);
      __Pyx_GOTREF(__pyx_t_6);
      __Pyx_GOTREF(__pyx_t_7);

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":941
 *         __pyx_import_array()
 *     except Exception:
 *         raise ImportError("numpy.core.multiarray failed to import")             # <<<<<<<<<<<<<<
 * 
 * cdef inline int import_umath() except -1:
 */
      __pyx_t_8 = __Pyx_PyObject_Call(__pyx_builtin_ImportError, __pyx_tuple_, NULL); if (unlikely(!__pyx_t_8)) __PYX_ERR(1, 941, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_8);
      __Pyx_Raise(__pyx_t_8, 0, 0, 0);
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      __PYX_ERR(1, 941, __pyx_L5_except_error)
    }
    goto __pyx_L5_except_error;
    __pyx_L5_except_error:;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":938
 * # Cython code.
 * cdef inline int import_array() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         __pyx_import_array()
 *     except Exception:
 */
    __Pyx_XGIVEREF(__pyx_t_1);
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);
    goto __pyx_L1_error;
    __pyx_L8_try_end:;
  }

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":937
 * # Versions of the import_* functions which are more suitable for
 * # Cython code.
 * cdef inline int import_array() except -1:             # <<<<<<<<<<<<<<
 *     try:
 *         __pyx_import_array()
 */

  /* function exit code */
  __pyx_r = 0;
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_AddTraceback("numpy.import_array", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = -1;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":943
 *         raise ImportError("numpy.core.multiarray failed to import")
 * 
 * cdef inline int import_umath() except -1:             # <<<<<<<<<<<<<<
 *     try:
 *         _import_umath()
 */

static CYTHON_INLINE int __pyx_f_5numpy_import_umath(void) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  int __pyx_t_4;
  PyObject *__pyx_t_5 = NULL;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("import_umath", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":944
 * 
 * cdef inline int import_umath() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         _import_umath()
 *     except Exception:
 */
  {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ExceptionSave(&__pyx_t_1, &__pyx_t_2, &__pyx_t_3);
    __Pyx_XGOTREF(__pyx_t_1);
    __Pyx_XGOTREF(__pyx_t_2);
    __Pyx_XGOTREF(__pyx_t_3);
    /*try:*/ {

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":945
 * cdef inline int import_umath() except -1:
 *     try:
 *         _import_umath()             # <<<<<<<<<<<<<<
 *     except Exception:
 *         raise ImportError("numpy.core.umath failed to import")
 */
      __pyx_t_4 = _import_umath(); if (unlikely(__pyx_t_4 == ((int)-1))) __PYX_ERR(1, 945, __pyx_L3_error)

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":944
 * 
 * cdef inline int import_umath() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         _import_umath()
 *     except Exception:
 */
    }
    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
    __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
    goto __pyx_L8_try_end;
    __pyx_L3_error:;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":946
 *     try:
 *         _import_umath()
 *     except Exception:             # <<<<<<<<<<<<<<
 *         raise ImportError("numpy.core.umath failed to import")
 * 
 */
    __pyx_t_4 = __Pyx_PyErr_ExceptionMatches(((PyObject *)(&((PyTypeObject*)PyExc_Exception)[0])));
    if (__pyx_t_4) {
      __Pyx_AddTraceback("numpy.import_umath", __pyx_clineno, __pyx_lineno, __pyx_filename);
      if (__Pyx_GetException(&__pyx_t_5, &__pyx_t_6, &__pyx_t_7) < 0) __PYX_ERR(1, 946, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_5);
      __Pyx_GOTREF(__pyx_t_6);
      __Pyx_GOTREF(__pyx_t_7);

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":947
 *         _import_umath()
 *     except Exception:
 *         raise ImportError("numpy.core.umath failed to import")             # <<<<<<<<<<<<<<
 * 
 * cdef inline int import_ufunc() except -1:
 */
      __pyx_t_8 = __Pyx_PyObject_Call(__pyx_builtin_ImportError, __pyx_tuple__2, NULL); if (unlikely(!__pyx_t_8)) __PYX_ERR(1, 947, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_8);
      __Pyx_Raise(__pyx_t_8, 0, 0, 0);
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      __PYX_ERR(1, 947, __pyx_L5_except_error)
    }
    goto __pyx_L5_except_error;
    __pyx_L5_except_error:;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":944
 * 
 * cdef inline int import_umath() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         _import_umath()
 *     except Exception:
 */
    __Pyx_XGIVEREF(__pyx_t_1);
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);
    goto __pyx_L1_error;
    __pyx_L8_try_end:;
  }

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":943
 *         raise ImportError("numpy.core.multiarray failed to import")
 * 
 * cdef inline int import_umath() except -1:             # <<<<<<<<<<<<<<
 *     try:
 *         _import_umath()
 */

  /* function exit code */
  __pyx_r = 0;
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_AddTraceback("numpy.import_umath", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = -1;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":949
 *         raise ImportError("numpy.core.umath failed to import")
 * 
 * cdef inline int import_ufunc() except -1:             # <<<<<<<<<<<<<<
 *     try:
 *         _import_umath()
 */

static CYTHON_INLINE int __pyx_f_5numpy_import_ufunc(void) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  PyObject *__pyx_t_3 = NULL;
  int __pyx_t_4;
  PyObject *__pyx_t_5 = NULL;
  PyObject *__pyx_t_6 = NULL;
  PyObject *__pyx_t_7 = NULL;
  PyObject *__pyx_t_8 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("import_ufunc", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":950
 * 
 * cdef inline int import_ufunc() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         _import_umath()
 *     except Exception:
 */
  {
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ExceptionSave(&__pyx_t_1, &__pyx_t_2, &__pyx_t_3);
    __Pyx_XGOTREF(__pyx_t_1);
    __Pyx_XGOTREF(__pyx_t_2);
    __Pyx_XGOTREF(__pyx_t_3);
    /*try:*/ {

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":951
 * cdef inline int import_ufunc() except -1:
 *     try:
 *         _import_umath()             # <<<<<<<<<<<<<<
 *     except Exception:
 *         raise ImportError("numpy.core.umath failed to import")
 */
      __pyx_t_4 = _import_umath(); if (unlikely(__pyx_t_4 == ((int)-1))) __PYX_ERR(1, 951, __pyx_L3_error)

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":950
 * 
 * cdef inline int import_ufunc() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         _import_umath()
 *     except Exception:
 */
    }
    __Pyx_XDECREF(__pyx_t_1); __pyx_t_1 = 0;
    __Pyx_XDECREF(__pyx_t_2); __pyx_t_2 = 0;
    __Pyx_XDECREF(__pyx_t_3); __pyx_t_3 = 0;
    goto __pyx_L8_try_end;
    __pyx_L3_error:;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":952
 *     try:
 *         _import_umath()
 *     except Exception:             # <<<<<<<<<<<<<<
 *         raise ImportError("numpy.core.umath failed to import")
 * 
 */
    __pyx_t_4 = __Pyx_PyErr_ExceptionMatches(((PyObject *)(&((PyTypeObject*)PyExc_Exception)[0])));
    if (__pyx_t_4) {
      __Pyx_AddTraceback("numpy.import_ufunc", __pyx_clineno, __pyx_lineno, __pyx_filename);
      if (__Pyx_GetException(&__pyx_t_5, &__pyx_t_6, &__pyx_t_7) < 0) __PYX_ERR(1, 952, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_5);
      __Pyx_GOTREF(__pyx_t_6);
      __Pyx_GOTREF(__pyx_t_7);

      /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":953
 *         _import_umath()
 *     except Exception:
 *         raise ImportError("numpy.core.umath failed to import")             # <<<<<<<<<<<<<<
 * 
 * cdef extern from *:
 */
      __pyx_t_8 = __Pyx_PyObject_Call(__pyx_builtin_ImportError, __pyx_tuple__2, NULL); if (unlikely(!__pyx_t_8)) __PYX_ERR(1, 953, __pyx_L5_except_error)
      __Pyx_GOTREF(__pyx_t_8);
      __Pyx_Raise(__pyx_t_8, 0, 0, 0);
      __Pyx_DECREF(__pyx_t_8); __pyx_t_8 = 0;
      __PYX_ERR(1, 953, __pyx_L5_except_error)
    }
    goto __pyx_L5_except_error;
    __pyx_L5_except_error:;

    /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":950
 * 
 * cdef inline int import_ufunc() except -1:
 *     try:             # <<<<<<<<<<<<<<
 *         _import_umath()
 *     except Exception:
 */
    __Pyx_XGIVEREF(__pyx_t_1);
    __Pyx_XGIVEREF(__pyx_t_2);
    __Pyx_XGIVEREF(__pyx_t_3);
    __Pyx_ExceptionReset(__pyx_t_1, __pyx_t_2, __pyx_t_3);
    goto __pyx_L1_error;
    __pyx_L8_try_end:;
  }

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":949
 *         raise ImportError("numpy.core.umath failed to import")
 * 
 * cdef inline int import_ufunc() except -1:             # <<<<<<<<<<<<<<
 *     try:
 *         _import_umath()
 */

  /* function exit code */
  __pyx_r = 0;
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_5);
  __Pyx_XDECREF(__pyx_t_6);
  __Pyx_XDECREF(__pyx_t_7);
  __Pyx_XDECREF(__pyx_t_8);
  __Pyx_AddTraceback("numpy.import_ufunc", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = -1;
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":963
 * 
 * 
 * cdef inline bint is_timedelta64_object(object obj):             # <<<<<<<<<<<<<<
 *     """
 *     Cython equivalent of `isinstance(obj, np.timedelta64)`
 */

static CYTHON_INLINE int __pyx_f_5numpy_is_timedelta64_object(PyObject *__pyx_v_obj) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("is_timedelta64_object", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":975
 *     bool
 *     """
 *     return PyObject_TypeCheck(obj, &PyTimedeltaArrType_Type)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = PyObject_TypeCheck(__pyx_v_obj, (&PyTimedeltaArrType_Type));
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":963
 * 
 * 
 * cdef inline bint is_timedelta64_object(object obj):             # <<<<<<<<<<<<<<
 *     """
 *     Cython equivalent of `isinstance(obj, np.timedelta64)`
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":978
 * 
 * 
 * cdef inline bint is_datetime64_object(object obj):             # <<<<<<<<<<<<<<
 *     """
 *     Cython equivalent of `isinstance(obj, np.datetime64)`
 */

static CYTHON_INLINE int __pyx_f_5numpy_is_datetime64_object(PyObject *__pyx_v_obj) {
  int __pyx_r;
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("is_datetime64_object", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":990
 *     bool
 *     """
 *     return PyObject_TypeCheck(obj, &PyDatetimeArrType_Type)             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = PyObject_TypeCheck(__pyx_v_obj, (&PyDatetimeArrType_Type));
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":978
 * 
 * 
 * cdef inline bint is_datetime64_object(object obj):             # <<<<<<<<<<<<<<
 *     """
 *     Cython equivalent of `isinstance(obj, np.datetime64)`
 */

  /* function exit code */
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":993
 * 
 * 
 * cdef inline npy_datetime get_datetime64_value(object obj) nogil:             # <<<<<<<<<<<<<<
 *     """
 *     returns the int64 value underlying scalar numpy datetime64 object
 */

static CYTHON_INLINE npy_datetime __pyx_f_5numpy_get_datetime64_value(PyObject *__pyx_v_obj) {
  npy_datetime __pyx_r;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1000
 *     also needed.  That can be found using `get_datetime64_unit`.
 *     """
 *     return (<PyDatetimeScalarObject*>obj).obval             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = ((PyDatetimeScalarObject *)__pyx_v_obj)->obval;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":993
 * 
 * 
 * cdef inline npy_datetime get_datetime64_value(object obj) nogil:             # <<<<<<<<<<<<<<
 *     """
 *     returns the int64 value underlying scalar numpy datetime64 object
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1003
 * 
 * 
 * cdef inline npy_timedelta get_timedelta64_value(object obj) nogil:             # <<<<<<<<<<<<<<
 *     """
 *     returns the int64 value underlying scalar numpy timedelta64 object
 */

static CYTHON_INLINE npy_timedelta __pyx_f_5numpy_get_timedelta64_value(PyObject *__pyx_v_obj) {
  npy_timedelta __pyx_r;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1007
 *     returns the int64 value underlying scalar numpy timedelta64 object
 *     """
 *     return (<PyTimedeltaScalarObject*>obj).obval             # <<<<<<<<<<<<<<
 * 
 * 
 */
  __pyx_r = ((PyTimedeltaScalarObject *)__pyx_v_obj)->obval;
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1003
 * 
 * 
 * cdef inline npy_timedelta get_timedelta64_value(object obj) nogil:             # <<<<<<<<<<<<<<
 *     """
 *     returns the int64 value underlying scalar numpy timedelta64 object
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1010
 * 
 * 
 * cdef inline NPY_DATETIMEUNIT get_datetime64_unit(object obj) nogil:             # <<<<<<<<<<<<<<
 *     """
 *     returns the unit part of the dtype for a numpy datetime64 object.
 */

static CYTHON_INLINE NPY_DATETIMEUNIT __pyx_f_5numpy_get_datetime64_unit(PyObject *__pyx_v_obj) {
  NPY_DATETIMEUNIT __pyx_r;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1014
 *     returns the unit part of the dtype for a numpy datetime64 object.
 *     """
 *     return <NPY_DATETIMEUNIT>(<PyDatetimeScalarObject*>obj).obmeta.base             # <<<<<<<<<<<<<<
 */
  __pyx_r = ((NPY_DATETIMEUNIT)((PyDatetimeScalarObject *)__pyx_v_obj)->obmeta.base);
  goto __pyx_L0;

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":1010
 * 
 * 
 * cdef inline NPY_DATETIMEUNIT get_datetime64_unit(object obj) nogil:             # <<<<<<<<<<<<<<
 *     """
 *     returns the unit part of the dtype for a numpy datetime64 object.
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "lexeme.pxd":30
 * 
 *     @staticmethod
 *     cdef inline Lexeme from_ptr(LexemeC* lex, Vocab vocab):             # <<<<<<<<<<<<<<
 *         cdef Lexeme self = Lexeme.__new__(Lexeme, vocab, lex.orth)
 *         self.c = lex
 */

static CYTHON_INLINE struct __pyx_obj_5spacy_6lexeme_Lexeme *__pyx_f_5spacy_6lexeme_6Lexeme_from_ptr(struct __pyx_t_5spacy_7structs_LexemeC *__pyx_v_lex, struct __pyx_obj_5spacy_5vocab_Vocab *__pyx_v_vocab) {
  struct __pyx_obj_5spacy_6lexeme_Lexeme *__pyx_v_self = 0;
  struct __pyx_obj_5spacy_6lexeme_Lexeme *__pyx_r = NULL;
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  PyObject *__pyx_t_2 = NULL;
  __pyx_t_5spacy_8typedefs_attr_t __pyx_t_3;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("from_ptr", 0);

  /* "lexeme.pxd":31
 *     @staticmethod
 *     cdef inline Lexeme from_ptr(LexemeC* lex, Vocab vocab):
 *         cdef Lexeme self = Lexeme.__new__(Lexeme, vocab, lex.orth)             # <<<<<<<<<<<<<<
 *         self.c = lex
 *         self.vocab = vocab
 */
  __pyx_t_1 = __Pyx_PyInt_From_uint64_t(__pyx_v_lex->orth); if (unlikely(!__pyx_t_1)) __PYX_ERR(2, 31, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_t_2 = PyTuple_New(2); if (unlikely(!__pyx_t_2)) __PYX_ERR(2, 31, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_2);
  __Pyx_INCREF(((PyObject *)__pyx_v_vocab));
  __Pyx_GIVEREF(((PyObject *)__pyx_v_vocab));
  PyTuple_SET_ITEM(__pyx_t_2, 0, ((PyObject *)__pyx_v_vocab));
  __Pyx_GIVEREF(__pyx_t_1);
  PyTuple_SET_ITEM(__pyx_t_2, 1, __pyx_t_1);
  __pyx_t_1 = 0;
  __pyx_t_1 = __Pyx_tp_new(((PyObject *)__pyx_ptype_5spacy_6lexeme_Lexeme), ((PyObject*)__pyx_t_2)); if (unlikely(!__pyx_t_1)) __PYX_ERR(2, 31, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __Pyx_DECREF(__pyx_t_2); __pyx_t_2 = 0;
  if (!(likely(__Pyx_TypeTest(__pyx_t_1, __pyx_ptype_5spacy_6lexeme_Lexeme)))) __PYX_ERR(2, 31, __pyx_L1_error)
  __pyx_v_self = ((struct __pyx_obj_5spacy_6lexeme_Lexeme *)__pyx_t_1);
  __pyx_t_1 = 0;

  /* "lexeme.pxd":32
 *     cdef inline Lexeme from_ptr(LexemeC* lex, Vocab vocab):
 *         cdef Lexeme self = Lexeme.__new__(Lexeme, vocab, lex.orth)
 *         self.c = lex             # <<<<<<<<<<<<<<
 *         self.vocab = vocab
 *         self.orth = lex.orth
 */
  __pyx_v_self->c = __pyx_v_lex;

  /* "lexeme.pxd":33
 *         cdef Lexeme self = Lexeme.__new__(Lexeme, vocab, lex.orth)
 *         self.c = lex
 *         self.vocab = vocab             # <<<<<<<<<<<<<<
 *         self.orth = lex.orth
 *         return self
 */
  __Pyx_INCREF(((PyObject *)__pyx_v_vocab));
  __Pyx_GIVEREF(((PyObject *)__pyx_v_vocab));
  __Pyx_GOTREF(__pyx_v_self->vocab);
  __Pyx_DECREF(((PyObject *)__pyx_v_self->vocab));
  __pyx_v_self->vocab = __pyx_v_vocab;

  /* "lexeme.pxd":34
 *         self.c = lex
 *         self.vocab = vocab
 *         self.orth = lex.orth             # <<<<<<<<<<<<<<
 *         return self
 * 
 */
  __pyx_t_3 = __pyx_v_lex->orth;
  __pyx_v_self->orth = __pyx_t_3;

  /* "lexeme.pxd":35
 *         self.vocab = vocab
 *         self.orth = lex.orth
 *         return self             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  __Pyx_XDECREF(((PyObject *)__pyx_r));
  __Pyx_INCREF(((PyObject *)__pyx_v_self));
  __pyx_r = __pyx_v_self;
  goto __pyx_L0;

  /* "lexeme.pxd":30
 * 
 *     @staticmethod
 *     cdef inline Lexeme from_ptr(LexemeC* lex, Vocab vocab):             # <<<<<<<<<<<<<<
 *         cdef Lexeme self = Lexeme.__new__(Lexeme, vocab, lex.orth)
 *         self.c = lex
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_XDECREF(__pyx_t_2);
  __Pyx_AddTraceback("spacy.lexeme.Lexeme.from_ptr", __pyx_clineno, __pyx_lineno, __pyx_filename);
  __pyx_r = 0;
  __pyx_L0:;
  __Pyx_XDECREF((PyObject *)__pyx_v_self);
  __Pyx_XGIVEREF((PyObject *)__pyx_r);
  __Pyx_RefNannyFinishContext();
  return __pyx_r;
}

/* "lexeme.pxd":38
 * 
 *     @staticmethod
 *     cdef inline void set_struct_attr(LexemeC* lex, attr_id_t name, attr_t value) nogil:             # <<<<<<<<<<<<<<
 *         if name < (sizeof(flags_t) * 8):
 *             Lexeme.c_set_flag(lex, name, value)
 */

static CYTHON_INLINE void __pyx_f_5spacy_6lexeme_6Lexeme_set_struct_attr(struct __pyx_t_5spacy_7structs_LexemeC *__pyx_v_lex, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_name, __pyx_t_5spacy_8typedefs_attr_t __pyx_v_value) {
  int __pyx_t_1;

  /* "lexeme.pxd":39
 *     @staticmethod
 *     cdef inline void set_struct_attr(LexemeC* lex, attr_id_t name, attr_t value) nogil:
 *         if name < (sizeof(flags_t) * 8):             # <<<<<<<<<<<<<<
 *             Lexeme.c_set_flag(lex, name, value)
 *         elif name == ID:
 */
  __pyx_t_1 = ((__pyx_v_name < ((sizeof(__pyx_t_5spacy_8typedefs_flags_t)) * 8)) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":40
 *     cdef inline void set_struct_attr(LexemeC* lex, attr_id_t name, attr_t value) nogil:
 *         if name < (sizeof(flags_t) * 8):
 *             Lexeme.c_set_flag(lex, name, value)             # <<<<<<<<<<<<<<
 *         elif name == ID:
 *             lex.id = value
 */
    (void)(__pyx_f_5spacy_6lexeme_6Lexeme_c_set_flag(__pyx_v_lex, __pyx_v_name, __pyx_v_value));

    /* "lexeme.pxd":39
 *     @staticmethod
 *     cdef inline void set_struct_attr(LexemeC* lex, attr_id_t name, attr_t value) nogil:
 *         if name < (sizeof(flags_t) * 8):             # <<<<<<<<<<<<<<
 *             Lexeme.c_set_flag(lex, name, value)
 *         elif name == ID:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":41
 *         if name < (sizeof(flags_t) * 8):
 *             Lexeme.c_set_flag(lex, name, value)
 *         elif name == ID:             # <<<<<<<<<<<<<<
 *             lex.id = value
 *         elif name == LOWER:
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_ID) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":42
 *             Lexeme.c_set_flag(lex, name, value)
 *         elif name == ID:
 *             lex.id = value             # <<<<<<<<<<<<<<
 *         elif name == LOWER:
 *             lex.lower = value
 */
    __pyx_v_lex->id = __pyx_v_value;

    /* "lexeme.pxd":41
 *         if name < (sizeof(flags_t) * 8):
 *             Lexeme.c_set_flag(lex, name, value)
 *         elif name == ID:             # <<<<<<<<<<<<<<
 *             lex.id = value
 *         elif name == LOWER:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":43
 *         elif name == ID:
 *             lex.id = value
 *         elif name == LOWER:             # <<<<<<<<<<<<<<
 *             lex.lower = value
 *         elif name == NORM:
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_LOWER) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":44
 *             lex.id = value
 *         elif name == LOWER:
 *             lex.lower = value             # <<<<<<<<<<<<<<
 *         elif name == NORM:
 *             lex.norm = value
 */
    __pyx_v_lex->lower = __pyx_v_value;

    /* "lexeme.pxd":43
 *         elif name == ID:
 *             lex.id = value
 *         elif name == LOWER:             # <<<<<<<<<<<<<<
 *             lex.lower = value
 *         elif name == NORM:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":45
 *         elif name == LOWER:
 *             lex.lower = value
 *         elif name == NORM:             # <<<<<<<<<<<<<<
 *             lex.norm = value
 *         elif name == SHAPE:
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_NORM) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":46
 *             lex.lower = value
 *         elif name == NORM:
 *             lex.norm = value             # <<<<<<<<<<<<<<
 *         elif name == SHAPE:
 *             lex.shape = value
 */
    __pyx_v_lex->norm = __pyx_v_value;

    /* "lexeme.pxd":45
 *         elif name == LOWER:
 *             lex.lower = value
 *         elif name == NORM:             # <<<<<<<<<<<<<<
 *             lex.norm = value
 *         elif name == SHAPE:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":47
 *         elif name == NORM:
 *             lex.norm = value
 *         elif name == SHAPE:             # <<<<<<<<<<<<<<
 *             lex.shape = value
 *         elif name == PREFIX:
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_SHAPE) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":48
 *             lex.norm = value
 *         elif name == SHAPE:
 *             lex.shape = value             # <<<<<<<<<<<<<<
 *         elif name == PREFIX:
 *             lex.prefix = value
 */
    __pyx_v_lex->shape = __pyx_v_value;

    /* "lexeme.pxd":47
 *         elif name == NORM:
 *             lex.norm = value
 *         elif name == SHAPE:             # <<<<<<<<<<<<<<
 *             lex.shape = value
 *         elif name == PREFIX:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":49
 *         elif name == SHAPE:
 *             lex.shape = value
 *         elif name == PREFIX:             # <<<<<<<<<<<<<<
 *             lex.prefix = value
 *         elif name == SUFFIX:
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_PREFIX) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":50
 *             lex.shape = value
 *         elif name == PREFIX:
 *             lex.prefix = value             # <<<<<<<<<<<<<<
 *         elif name == SUFFIX:
 *             lex.suffix = value
 */
    __pyx_v_lex->prefix = __pyx_v_value;

    /* "lexeme.pxd":49
 *         elif name == SHAPE:
 *             lex.shape = value
 *         elif name == PREFIX:             # <<<<<<<<<<<<<<
 *             lex.prefix = value
 *         elif name == SUFFIX:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":51
 *         elif name == PREFIX:
 *             lex.prefix = value
 *         elif name == SUFFIX:             # <<<<<<<<<<<<<<
 *             lex.suffix = value
 *         elif name == LANG:
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_SUFFIX) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":52
 *             lex.prefix = value
 *         elif name == SUFFIX:
 *             lex.suffix = value             # <<<<<<<<<<<<<<
 *         elif name == LANG:
 *             lex.lang = value
 */
    __pyx_v_lex->suffix = __pyx_v_value;

    /* "lexeme.pxd":51
 *         elif name == PREFIX:
 *             lex.prefix = value
 *         elif name == SUFFIX:             # <<<<<<<<<<<<<<
 *             lex.suffix = value
 *         elif name == LANG:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":53
 *         elif name == SUFFIX:
 *             lex.suffix = value
 *         elif name == LANG:             # <<<<<<<<<<<<<<
 *             lex.lang = value
 * 
 */
  __pyx_t_1 = ((__pyx_v_name == __pyx_e_5spacy_5attrs_LANG) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":54
 *             lex.suffix = value
 *         elif name == LANG:
 *             lex.lang = value             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
    __pyx_v_lex->lang = __pyx_v_value;

    /* "lexeme.pxd":53
 *         elif name == SUFFIX:
 *             lex.suffix = value
 *         elif name == LANG:             # <<<<<<<<<<<<<<
 *             lex.lang = value
 * 
 */
  }
  __pyx_L3:;

  /* "lexeme.pxd":38
 * 
 *     @staticmethod
 *     cdef inline void set_struct_attr(LexemeC* lex, attr_id_t name, attr_t value) nogil:             # <<<<<<<<<<<<<<
 *         if name < (sizeof(flags_t) * 8):
 *             Lexeme.c_set_flag(lex, name, value)
 */

  /* function exit code */
}

/* "lexeme.pxd":57
 * 
 *     @staticmethod
 *     cdef inline attr_t get_struct_attr(const LexemeC* lex, attr_id_t feat_name) nogil:             # <<<<<<<<<<<<<<
 *         if feat_name < (sizeof(flags_t) * 8):
 *             if Lexeme.c_check_flag(lex, feat_name):
 */

static CYTHON_INLINE __pyx_t_5spacy_8typedefs_attr_t __pyx_f_5spacy_6lexeme_6Lexeme_get_struct_attr(struct __pyx_t_5spacy_7structs_LexemeC const *__pyx_v_lex, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_feat_name) {
  __pyx_t_5spacy_8typedefs_attr_t __pyx_r;
  int __pyx_t_1;

  /* "lexeme.pxd":58
 *     @staticmethod
 *     cdef inline attr_t get_struct_attr(const LexemeC* lex, attr_id_t feat_name) nogil:
 *         if feat_name < (sizeof(flags_t) * 8):             # <<<<<<<<<<<<<<
 *             if Lexeme.c_check_flag(lex, feat_name):
 *                 return 1
 */
  __pyx_t_1 = ((__pyx_v_feat_name < ((sizeof(__pyx_t_5spacy_8typedefs_flags_t)) * 8)) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":59
 *     cdef inline attr_t get_struct_attr(const LexemeC* lex, attr_id_t feat_name) nogil:
 *         if feat_name < (sizeof(flags_t) * 8):
 *             if Lexeme.c_check_flag(lex, feat_name):             # <<<<<<<<<<<<<<
 *                 return 1
 *             else:
 */
    __pyx_t_1 = (__pyx_f_5spacy_6lexeme_6Lexeme_c_check_flag(__pyx_v_lex, __pyx_v_feat_name) != 0);
    if (__pyx_t_1) {

      /* "lexeme.pxd":60
 *         if feat_name < (sizeof(flags_t) * 8):
 *             if Lexeme.c_check_flag(lex, feat_name):
 *                 return 1             # <<<<<<<<<<<<<<
 *             else:
 *                 return 0
 */
      __pyx_r = 1;
      goto __pyx_L0;

      /* "lexeme.pxd":59
 *     cdef inline attr_t get_struct_attr(const LexemeC* lex, attr_id_t feat_name) nogil:
 *         if feat_name < (sizeof(flags_t) * 8):
 *             if Lexeme.c_check_flag(lex, feat_name):             # <<<<<<<<<<<<<<
 *                 return 1
 *             else:
 */
    }

    /* "lexeme.pxd":62
 *                 return 1
 *             else:
 *                 return 0             # <<<<<<<<<<<<<<
 *         elif feat_name == ID:
 *             return lex.id
 */
    /*else*/ {
      __pyx_r = 0;
      goto __pyx_L0;
    }

    /* "lexeme.pxd":58
 *     @staticmethod
 *     cdef inline attr_t get_struct_attr(const LexemeC* lex, attr_id_t feat_name) nogil:
 *         if feat_name < (sizeof(flags_t) * 8):             # <<<<<<<<<<<<<<
 *             if Lexeme.c_check_flag(lex, feat_name):
 *                 return 1
 */
  }

  /* "lexeme.pxd":63
 *             else:
 *                 return 0
 *         elif feat_name == ID:             # <<<<<<<<<<<<<<
 *             return lex.id
 *         elif feat_name == ORTH:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_ID) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":64
 *                 return 0
 *         elif feat_name == ID:
 *             return lex.id             # <<<<<<<<<<<<<<
 *         elif feat_name == ORTH:
 *             return lex.orth
 */
    __pyx_r = __pyx_v_lex->id;
    goto __pyx_L0;

    /* "lexeme.pxd":63
 *             else:
 *                 return 0
 *         elif feat_name == ID:             # <<<<<<<<<<<<<<
 *             return lex.id
 *         elif feat_name == ORTH:
 */
  }

  /* "lexeme.pxd":65
 *         elif feat_name == ID:
 *             return lex.id
 *         elif feat_name == ORTH:             # <<<<<<<<<<<<<<
 *             return lex.orth
 *         elif feat_name == LOWER:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_ORTH) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":66
 *             return lex.id
 *         elif feat_name == ORTH:
 *             return lex.orth             # <<<<<<<<<<<<<<
 *         elif feat_name == LOWER:
 *             return lex.lower
 */
    __pyx_r = __pyx_v_lex->orth;
    goto __pyx_L0;

    /* "lexeme.pxd":65
 *         elif feat_name == ID:
 *             return lex.id
 *         elif feat_name == ORTH:             # <<<<<<<<<<<<<<
 *             return lex.orth
 *         elif feat_name == LOWER:
 */
  }

  /* "lexeme.pxd":67
 *         elif feat_name == ORTH:
 *             return lex.orth
 *         elif feat_name == LOWER:             # <<<<<<<<<<<<<<
 *             return lex.lower
 *         elif feat_name == NORM:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_LOWER) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":68
 *             return lex.orth
 *         elif feat_name == LOWER:
 *             return lex.lower             # <<<<<<<<<<<<<<
 *         elif feat_name == NORM:
 *             return lex.norm
 */
    __pyx_r = __pyx_v_lex->lower;
    goto __pyx_L0;

    /* "lexeme.pxd":67
 *         elif feat_name == ORTH:
 *             return lex.orth
 *         elif feat_name == LOWER:             # <<<<<<<<<<<<<<
 *             return lex.lower
 *         elif feat_name == NORM:
 */
  }

  /* "lexeme.pxd":69
 *         elif feat_name == LOWER:
 *             return lex.lower
 *         elif feat_name == NORM:             # <<<<<<<<<<<<<<
 *             return lex.norm
 *         elif feat_name == SHAPE:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_NORM) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":70
 *             return lex.lower
 *         elif feat_name == NORM:
 *             return lex.norm             # <<<<<<<<<<<<<<
 *         elif feat_name == SHAPE:
 *             return lex.shape
 */
    __pyx_r = __pyx_v_lex->norm;
    goto __pyx_L0;

    /* "lexeme.pxd":69
 *         elif feat_name == LOWER:
 *             return lex.lower
 *         elif feat_name == NORM:             # <<<<<<<<<<<<<<
 *             return lex.norm
 *         elif feat_name == SHAPE:
 */
  }

  /* "lexeme.pxd":71
 *         elif feat_name == NORM:
 *             return lex.norm
 *         elif feat_name == SHAPE:             # <<<<<<<<<<<<<<
 *             return lex.shape
 *         elif feat_name == PREFIX:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_SHAPE) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":72
 *             return lex.norm
 *         elif feat_name == SHAPE:
 *             return lex.shape             # <<<<<<<<<<<<<<
 *         elif feat_name == PREFIX:
 *             return lex.prefix
 */
    __pyx_r = __pyx_v_lex->shape;
    goto __pyx_L0;

    /* "lexeme.pxd":71
 *         elif feat_name == NORM:
 *             return lex.norm
 *         elif feat_name == SHAPE:             # <<<<<<<<<<<<<<
 *             return lex.shape
 *         elif feat_name == PREFIX:
 */
  }

  /* "lexeme.pxd":73
 *         elif feat_name == SHAPE:
 *             return lex.shape
 *         elif feat_name == PREFIX:             # <<<<<<<<<<<<<<
 *             return lex.prefix
 *         elif feat_name == SUFFIX:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_PREFIX) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":74
 *             return lex.shape
 *         elif feat_name == PREFIX:
 *             return lex.prefix             # <<<<<<<<<<<<<<
 *         elif feat_name == SUFFIX:
 *             return lex.suffix
 */
    __pyx_r = __pyx_v_lex->prefix;
    goto __pyx_L0;

    /* "lexeme.pxd":73
 *         elif feat_name == SHAPE:
 *             return lex.shape
 *         elif feat_name == PREFIX:             # <<<<<<<<<<<<<<
 *             return lex.prefix
 *         elif feat_name == SUFFIX:
 */
  }

  /* "lexeme.pxd":75
 *         elif feat_name == PREFIX:
 *             return lex.prefix
 *         elif feat_name == SUFFIX:             # <<<<<<<<<<<<<<
 *             return lex.suffix
 *         elif feat_name == LENGTH:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_SUFFIX) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":76
 *             return lex.prefix
 *         elif feat_name == SUFFIX:
 *             return lex.suffix             # <<<<<<<<<<<<<<
 *         elif feat_name == LENGTH:
 *             return lex.length
 */
    __pyx_r = __pyx_v_lex->suffix;
    goto __pyx_L0;

    /* "lexeme.pxd":75
 *         elif feat_name == PREFIX:
 *             return lex.prefix
 *         elif feat_name == SUFFIX:             # <<<<<<<<<<<<<<
 *             return lex.suffix
 *         elif feat_name == LENGTH:
 */
  }

  /* "lexeme.pxd":77
 *         elif feat_name == SUFFIX:
 *             return lex.suffix
 *         elif feat_name == LENGTH:             # <<<<<<<<<<<<<<
 *             return lex.length
 *         elif feat_name == LANG:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_LENGTH) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":78
 *             return lex.suffix
 *         elif feat_name == LENGTH:
 *             return lex.length             # <<<<<<<<<<<<<<
 *         elif feat_name == LANG:
 *             return lex.lang
 */
    __pyx_r = __pyx_v_lex->length;
    goto __pyx_L0;

    /* "lexeme.pxd":77
 *         elif feat_name == SUFFIX:
 *             return lex.suffix
 *         elif feat_name == LENGTH:             # <<<<<<<<<<<<<<
 *             return lex.length
 *         elif feat_name == LANG:
 */
  }

  /* "lexeme.pxd":79
 *         elif feat_name == LENGTH:
 *             return lex.length
 *         elif feat_name == LANG:             # <<<<<<<<<<<<<<
 *             return lex.lang
 *         else:
 */
  __pyx_t_1 = ((__pyx_v_feat_name == __pyx_e_5spacy_5attrs_LANG) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":80
 *             return lex.length
 *         elif feat_name == LANG:
 *             return lex.lang             # <<<<<<<<<<<<<<
 *         else:
 *             return 0
 */
    __pyx_r = __pyx_v_lex->lang;
    goto __pyx_L0;

    /* "lexeme.pxd":79
 *         elif feat_name == LENGTH:
 *             return lex.length
 *         elif feat_name == LANG:             # <<<<<<<<<<<<<<
 *             return lex.lang
 *         else:
 */
  }

  /* "lexeme.pxd":82
 *             return lex.lang
 *         else:
 *             return 0             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  /*else*/ {
    __pyx_r = 0;
    goto __pyx_L0;
  }

  /* "lexeme.pxd":57
 * 
 *     @staticmethod
 *     cdef inline attr_t get_struct_attr(const LexemeC* lex, attr_id_t feat_name) nogil:             # <<<<<<<<<<<<<<
 *         if feat_name < (sizeof(flags_t) * 8):
 *             if Lexeme.c_check_flag(lex, feat_name):
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "lexeme.pxd":85
 * 
 *     @staticmethod
 *     cdef inline bint c_check_flag(const LexemeC* lexeme, attr_id_t flag_id) nogil:             # <<<<<<<<<<<<<<
 *         cdef flags_t one = 1
 *         if lexeme.flags & (one << flag_id):
 */

static CYTHON_INLINE int __pyx_f_5spacy_6lexeme_6Lexeme_c_check_flag(struct __pyx_t_5spacy_7structs_LexemeC const *__pyx_v_lexeme, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_flag_id) {
  __pyx_t_5spacy_8typedefs_flags_t __pyx_v_one;
  int __pyx_r;
  int __pyx_t_1;

  /* "lexeme.pxd":86
 *     @staticmethod
 *     cdef inline bint c_check_flag(const LexemeC* lexeme, attr_id_t flag_id) nogil:
 *         cdef flags_t one = 1             # <<<<<<<<<<<<<<
 *         if lexeme.flags & (one << flag_id):
 *             return True
 */
  __pyx_v_one = 1;

  /* "lexeme.pxd":87
 *     cdef inline bint c_check_flag(const LexemeC* lexeme, attr_id_t flag_id) nogil:
 *         cdef flags_t one = 1
 *         if lexeme.flags & (one << flag_id):             # <<<<<<<<<<<<<<
 *             return True
 *         else:
 */
  __pyx_t_1 = ((__pyx_v_lexeme->flags & (__pyx_v_one << __pyx_v_flag_id)) != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":88
 *         cdef flags_t one = 1
 *         if lexeme.flags & (one << flag_id):
 *             return True             # <<<<<<<<<<<<<<
 *         else:
 *             return False
 */
    __pyx_r = 1;
    goto __pyx_L0;

    /* "lexeme.pxd":87
 *     cdef inline bint c_check_flag(const LexemeC* lexeme, attr_id_t flag_id) nogil:
 *         cdef flags_t one = 1
 *         if lexeme.flags & (one << flag_id):             # <<<<<<<<<<<<<<
 *             return True
 *         else:
 */
  }

  /* "lexeme.pxd":90
 *             return True
 *         else:
 *             return False             # <<<<<<<<<<<<<<
 * 
 *     @staticmethod
 */
  /*else*/ {
    __pyx_r = 0;
    goto __pyx_L0;
  }

  /* "lexeme.pxd":85
 * 
 *     @staticmethod
 *     cdef inline bint c_check_flag(const LexemeC* lexeme, attr_id_t flag_id) nogil:             # <<<<<<<<<<<<<<
 *         cdef flags_t one = 1
 *         if lexeme.flags & (one << flag_id):
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "lexeme.pxd":93
 * 
 *     @staticmethod
 *     cdef inline bint c_set_flag(LexemeC* lex, attr_id_t flag_id, bint value) nogil:             # <<<<<<<<<<<<<<
 *         cdef flags_t one = 1
 *         if value:
 */

static CYTHON_INLINE int __pyx_f_5spacy_6lexeme_6Lexeme_c_set_flag(struct __pyx_t_5spacy_7structs_LexemeC *__pyx_v_lex, enum __pyx_t_5spacy_5attrs_attr_id_t __pyx_v_flag_id, int __pyx_v_value) {
  __pyx_t_5spacy_8typedefs_flags_t __pyx_v_one;
  int __pyx_r;
  int __pyx_t_1;

  /* "lexeme.pxd":94
 *     @staticmethod
 *     cdef inline bint c_set_flag(LexemeC* lex, attr_id_t flag_id, bint value) nogil:
 *         cdef flags_t one = 1             # <<<<<<<<<<<<<<
 *         if value:
 *             lex.flags |= one << flag_id
 */
  __pyx_v_one = 1;

  /* "lexeme.pxd":95
 *     cdef inline bint c_set_flag(LexemeC* lex, attr_id_t flag_id, bint value) nogil:
 *         cdef flags_t one = 1
 *         if value:             # <<<<<<<<<<<<<<
 *             lex.flags |= one << flag_id
 *         else:
 */
  __pyx_t_1 = (__pyx_v_value != 0);
  if (__pyx_t_1) {

    /* "lexeme.pxd":96
 *         cdef flags_t one = 1
 *         if value:
 *             lex.flags |= one << flag_id             # <<<<<<<<<<<<<<
 *         else:
 *             lex.flags &= ~(one << flag_id)
 */
    __pyx_v_lex->flags = (__pyx_v_lex->flags | (__pyx_v_one << __pyx_v_flag_id));

    /* "lexeme.pxd":95
 *     cdef inline bint c_set_flag(LexemeC* lex, attr_id_t flag_id, bint value) nogil:
 *         cdef flags_t one = 1
 *         if value:             # <<<<<<<<<<<<<<
 *             lex.flags |= one << flag_id
 *         else:
 */
    goto __pyx_L3;
  }

  /* "lexeme.pxd":98
 *             lex.flags |= one << flag_id
 *         else:
 *             lex.flags &= ~(one << flag_id)             # <<<<<<<<<<<<<<
 */
  /*else*/ {
    __pyx_v_lex->flags = (__pyx_v_lex->flags & (~(__pyx_v_one << __pyx_v_flag_id)));
  }
  __pyx_L3:;

  /* "lexeme.pxd":93
 * 
 *     @staticmethod
 *     cdef inline bint c_set_flag(LexemeC* lex, attr_id_t flag_id, bint value) nogil:             # <<<<<<<<<<<<<<
 *         cdef flags_t one = 1
 *         if value:
 */

  /* function exit code */
  __pyx_r = 0;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":20
 * 
 * 
 * cdef inline bint is_space_token(const TokenC* token) nogil:             # <<<<<<<<<<<<<<
 *     return Lexeme.c_check_flag(token.lex, IS_SPACE)
 * 
 */

static CYTHON_INLINE int __pyx_f_5spacy_8pipeline_17_parser_internals_6_state_is_space_token(struct __pyx_t_5spacy_7structs_TokenC const *__pyx_v_token) {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":21
 * 
 * cdef inline bint is_space_token(const TokenC* token) nogil:
 *     return Lexeme.c_check_flag(token.lex, IS_SPACE)             # <<<<<<<<<<<<<<
 * 
 * cdef struct ArcC:
 */
  __pyx_r = __pyx_f_5spacy_6lexeme_6Lexeme_c_check_flag(__pyx_v_token->lex, __pyx_e_5spacy_5attrs_IS_SPACE);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":20
 * 
 * 
 * cdef inline bint is_space_token(const TokenC* token) nogil:             # <<<<<<<<<<<<<<
 *     return Lexeme.c_check_flag(token.lex, IS_SPACE)
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":44
 *     int _b_i
 * 
 *     __init__(const TokenC* sent, int length) nogil:             # <<<<<<<<<<<<<<
 *         this._sent = sent
 *         this._heads = <int*>calloc(length, sizeof(int))
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::__pyx_f___init__StateC(struct __pyx_t_5spacy_7structs_TokenC const *__pyx_v_sent, int __pyx_v_length) {
  int __pyx_v_i;
  __Pyx_RefNannyDeclarations
  int __pyx_t_1;
  int __pyx_t_2;
  PyObject *__pyx_t_3;
  int __pyx_t_4;
  int __pyx_t_5;
  int __pyx_t_6;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  #ifdef WITH_THREAD
  PyGILState_STATE __pyx_gilstate_save;
  #endif
  __Pyx_RefNannySetupContext("<init>", 1);

  /* "spacy/pipeline/_parser_internals/_state.pxd":45
 * 
 *     __init__(const TokenC* sent, int length) nogil:
 *         this._sent = sent             # <<<<<<<<<<<<<<
 *         this._heads = <int*>calloc(length, sizeof(int))
 *         if not (this._sent and this._heads):
 */
  /*try:*/ {
    this->_sent = __pyx_v_sent;

    /* "spacy/pipeline/_parser_internals/_state.pxd":46
 *     __init__(const TokenC* sent, int length) nogil:
 *         this._sent = sent
 *         this._heads = <int*>calloc(length, sizeof(int))             # <<<<<<<<<<<<<<
 *         if not (this._sent and this._heads):
 *             with gil:
 */
    this->_heads = ((int *)calloc(__pyx_v_length, (sizeof(int))));

    /* "spacy/pipeline/_parser_internals/_state.pxd":47
 *         this._sent = sent
 *         this._heads = <int*>calloc(length, sizeof(int))
 *         if not (this._sent and this._heads):             # <<<<<<<<<<<<<<
 *             with gil:
 *                 PyErr_SetFromErrno(MemoryError)
 */
    __pyx_t_2 = (this->_sent != 0);
    if (__pyx_t_2) {
    } else {
      __pyx_t_1 = __pyx_t_2;
      goto __pyx_L7_bool_binop_done;
    }
    __pyx_t_2 = (this->_heads != 0);
    __pyx_t_1 = __pyx_t_2;
    __pyx_L7_bool_binop_done:;
    __pyx_t_2 = ((!__pyx_t_1) != 0);
    if (__pyx_t_2) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":48
 *         this._heads = <int*>calloc(length, sizeof(int))
 *         if not (this._sent and this._heads):
 *             with gil:             # <<<<<<<<<<<<<<
 *                 PyErr_SetFromErrno(MemoryError)
 *                 PyErr_CheckSignals()
 */
      {
          #ifdef WITH_THREAD
          PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
          #endif
          /*try:*/ {

            /* "spacy/pipeline/_parser_internals/_state.pxd":49
 *         if not (this._sent and this._heads):
 *             with gil:
 *                 PyErr_SetFromErrno(MemoryError)             # <<<<<<<<<<<<<<
 *                 PyErr_CheckSignals()
 *         this.offset = 0
 */
            __pyx_t_3 = PyErr_SetFromErrno(__pyx_builtin_MemoryError); if (unlikely(__pyx_t_3 == ((PyObject *)NULL))) __PYX_ERR(0, 49, __pyx_L10_error)

            /* "spacy/pipeline/_parser_internals/_state.pxd":50
 *             with gil:
 *                 PyErr_SetFromErrno(MemoryError)
 *                 PyErr_CheckSignals()             # <<<<<<<<<<<<<<
 *         this.offset = 0
 *         this.length = length
 */
            __pyx_t_4 = PyErr_CheckSignals(); if (unlikely(__pyx_t_4 == ((int)-1))) __PYX_ERR(0, 50, __pyx_L10_error)
          }

          /* "spacy/pipeline/_parser_internals/_state.pxd":48
 *         this._heads = <int*>calloc(length, sizeof(int))
 *         if not (this._sent and this._heads):
 *             with gil:             # <<<<<<<<<<<<<<
 *                 PyErr_SetFromErrno(MemoryError)
 *                 PyErr_CheckSignals()
 */
          /*finally:*/ {
            /*normal exit:*/{
              #ifdef WITH_THREAD
              __Pyx_PyGILState_Release(__pyx_gilstate_save);
              #endif
              goto __pyx_L11;
            }
            __pyx_L10_error: {
              #ifdef WITH_THREAD
              __Pyx_PyGILState_Release(__pyx_gilstate_save);
              #endif
              goto __pyx_L4_error;
            }
            __pyx_L11:;
          }
      }

      /* "spacy/pipeline/_parser_internals/_state.pxd":47
 *         this._sent = sent
 *         this._heads = <int*>calloc(length, sizeof(int))
 *         if not (this._sent and this._heads):             # <<<<<<<<<<<<<<
 *             with gil:
 *                 PyErr_SetFromErrno(MemoryError)
 */
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":51
 *                 PyErr_SetFromErrno(MemoryError)
 *                 PyErr_CheckSignals()
 *         this.offset = 0             # <<<<<<<<<<<<<<
 *         this.length = length
 *         this._b_i = 0
 */
    this->offset = 0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":52
 *                 PyErr_CheckSignals()
 *         this.offset = 0
 *         this.length = length             # <<<<<<<<<<<<<<
 *         this._b_i = 0
 *         for i in range(length):
 */
    this->length = __pyx_v_length;

    /* "spacy/pipeline/_parser_internals/_state.pxd":53
 *         this.offset = 0
 *         this.length = length
 *         this._b_i = 0             # <<<<<<<<<<<<<<
 *         for i in range(length):
 *             this._heads[i] = -1
 */
    this->_b_i = 0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":54
 *         this.length = length
 *         this._b_i = 0
 *         for i in range(length):             # <<<<<<<<<<<<<<
 *             this._heads[i] = -1
 *             this._unshiftable.push_back(0)
 */
    __pyx_t_4 = __pyx_v_length;
    __pyx_t_5 = __pyx_t_4;
    for (__pyx_t_6 = 0; __pyx_t_6 < __pyx_t_5; __pyx_t_6+=1) {
      __pyx_v_i = __pyx_t_6;

      /* "spacy/pipeline/_parser_internals/_state.pxd":55
 *         this._b_i = 0
 *         for i in range(length):
 *             this._heads[i] = -1             # <<<<<<<<<<<<<<
 *             this._unshiftable.push_back(0)
 *         memset(&this._empty_token, 0, sizeof(TokenC))
 */
      (this->_heads[__pyx_v_i]) = -1;

      /* "spacy/pipeline/_parser_internals/_state.pxd":56
 *         for i in range(length):
 *             this._heads[i] = -1
 *             this._unshiftable.push_back(0)             # <<<<<<<<<<<<<<
 *         memset(&this._empty_token, 0, sizeof(TokenC))
 *         this._empty_token.lex = &EMPTY_LEXEME
 */
      try {
        this->_unshiftable.push_back(0);
      } catch(...) {
        #ifdef WITH_THREAD
        PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
        #endif
        __Pyx_CppExn2PyErr();
        #ifdef WITH_THREAD
        __Pyx_PyGILState_Release(__pyx_gilstate_save);
        #endif
        __PYX_ERR(0, 56, __pyx_L4_error)
      }
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":57
 *             this._heads[i] = -1
 *             this._unshiftable.push_back(0)
 *         memset(&this._empty_token, 0, sizeof(TokenC))             # <<<<<<<<<<<<<<
 *         this._empty_token.lex = &EMPTY_LEXEME
 * 
 */
    (void)(memset((&this->_empty_token), 0, (sizeof(struct __pyx_t_5spacy_7structs_TokenC))));

    /* "spacy/pipeline/_parser_internals/_state.pxd":58
 *             this._unshiftable.push_back(0)
 *         memset(&this._empty_token, 0, sizeof(TokenC))
 *         this._empty_token.lex = &EMPTY_LEXEME             # <<<<<<<<<<<<<<
 * 
 *     __dealloc__():
 */
    this->_empty_token.lex = (&__pyx_v_5spacy_5vocab_EMPTY_LEXEME);
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":45
 * 
 *     __init__(const TokenC* sent, int length) nogil:
 *         this._sent = sent             # <<<<<<<<<<<<<<
 *         this._heads = <int*>calloc(length, sizeof(int))
 *         if not (this._sent and this._heads):
 */
  /*finally:*/ {
    /*normal exit:*/{
      #ifdef WITH_THREAD
      __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      goto __pyx_L5;
    }
    __pyx_L4_error: {
      #ifdef WITH_THREAD
      __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      goto __pyx_L1_error;
    }
    __pyx_L5:;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":44
 *     int _b_i
 * 
 *     __init__(const TokenC* sent, int length) nogil:             # <<<<<<<<<<<<<<
 *         this._sent = sent
 *         this._heads = <int*>calloc(length, sizeof(int))
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.<init>", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
  #ifdef WITH_THREAD
  __Pyx_PyGILState_Release(__pyx_gilstate_save);
  #endif
}

/* "spacy/pipeline/_parser_internals/_state.pxd":60
 *         this._empty_token.lex = &EMPTY_LEXEME
 * 
 *     __dealloc__():             # <<<<<<<<<<<<<<
 *         free(this._heads)
 * 
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::__pyx_f___dealloc__StateC(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("<del>", 0);

  /* "spacy/pipeline/_parser_internals/_state.pxd":61
 * 
 *     __dealloc__():
 *         free(this._heads)             # <<<<<<<<<<<<<<
 * 
 *     void set_context_tokens(int* ids, int n) nogil:
 */
  free(this->_heads);

  /* "spacy/pipeline/_parser_internals/_state.pxd":60
 *         this._empty_token.lex = &EMPTY_LEXEME
 * 
 *     __dealloc__():             # <<<<<<<<<<<<<<
 *         free(this._heads)
 * 
 */

  /* function exit code */
  __Pyx_RefNannyFinishContext();
}

/* "spacy/pipeline/_parser_internals/_state.pxd":63
 *         free(this._heads)
 * 
 *     void set_context_tokens(int* ids, int n) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i, j
 *         if n == 1:
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::set_context_tokens(int *__pyx_v_ids, int __pyx_v_n) {
  int __pyx_v_i;
  int __pyx_v_j;
  struct __pyx_t_5spacy_7structs_SpanC __pyx_v_ent;
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_t_3;
  int __pyx_t_4;
  int __pyx_t_5;
  int __pyx_t_6;

  /* "spacy/pipeline/_parser_internals/_state.pxd":65
 *     void set_context_tokens(int* ids, int n) nogil:
 *         cdef int i, j
 *         if n == 1:             # <<<<<<<<<<<<<<
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)
 */
  switch (__pyx_v_n) {
    case 1:

    /* "spacy/pipeline/_parser_internals/_state.pxd":66
 *         cdef int i, j
 *         if n == 1:
 *             if this.B(0) >= 0:             # <<<<<<<<<<<<<<
 *                 ids[0] = this.B(0)
 *             else:
 */
    __pyx_t_1 = ((this->B(0) >= 0) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":67
 *         if n == 1:
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)             # <<<<<<<<<<<<<<
 *             else:
 *                 ids[0] = -1
 */
      (__pyx_v_ids[0]) = this->B(0);

      /* "spacy/pipeline/_parser_internals/_state.pxd":66
 *         cdef int i, j
 *         if n == 1:
 *             if this.B(0) >= 0:             # <<<<<<<<<<<<<<
 *                 ids[0] = this.B(0)
 *             else:
 */
      goto __pyx_L3;
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":69
 *                 ids[0] = this.B(0)
 *             else:
 *                 ids[0] = -1             # <<<<<<<<<<<<<<
 *         elif n == 2:
 *             ids[0] = this.B(0)
 */
    /*else*/ {
      (__pyx_v_ids[0]) = -1;
    }
    __pyx_L3:;

    /* "spacy/pipeline/_parser_internals/_state.pxd":65
 *     void set_context_tokens(int* ids, int n) nogil:
 *         cdef int i, j
 *         if n == 1:             # <<<<<<<<<<<<<<
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)
 */
    break;
    case 2:

    /* "spacy/pipeline/_parser_internals/_state.pxd":71
 *                 ids[0] = -1
 *         elif n == 2:
 *             ids[0] = this.B(0)             # <<<<<<<<<<<<<<
 *             ids[1] = this.S(0)
 *         elif n == 3:
 */
    (__pyx_v_ids[0]) = this->B(0);

    /* "spacy/pipeline/_parser_internals/_state.pxd":72
 *         elif n == 2:
 *             ids[0] = this.B(0)
 *             ids[1] = this.S(0)             # <<<<<<<<<<<<<<
 *         elif n == 3:
 *             if this.B(0) >= 0:
 */
    (__pyx_v_ids[1]) = this->S(0);

    /* "spacy/pipeline/_parser_internals/_state.pxd":70
 *             else:
 *                 ids[0] = -1
 *         elif n == 2:             # <<<<<<<<<<<<<<
 *             ids[0] = this.B(0)
 *             ids[1] = this.S(0)
 */
    break;
    case 3:

    /* "spacy/pipeline/_parser_internals/_state.pxd":74
 *             ids[1] = this.S(0)
 *         elif n == 3:
 *             if this.B(0) >= 0:             # <<<<<<<<<<<<<<
 *                 ids[0] = this.B(0)
 *             else:
 */
    __pyx_t_1 = ((this->B(0) >= 0) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":75
 *         elif n == 3:
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)             # <<<<<<<<<<<<<<
 *             else:
 *                 ids[0] = -1
 */
      (__pyx_v_ids[0]) = this->B(0);

      /* "spacy/pipeline/_parser_internals/_state.pxd":74
 *             ids[1] = this.S(0)
 *         elif n == 3:
 *             if this.B(0) >= 0:             # <<<<<<<<<<<<<<
 *                 ids[0] = this.B(0)
 *             else:
 */
      goto __pyx_L4;
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":77
 *                 ids[0] = this.B(0)
 *             else:
 *                 ids[0] = -1             # <<<<<<<<<<<<<<
 *             # First word of entity, if any
 *             if this.entity_is_open():
 */
    /*else*/ {
      (__pyx_v_ids[0]) = -1;
    }
    __pyx_L4:;

    /* "spacy/pipeline/_parser_internals/_state.pxd":79
 *                 ids[0] = -1
 *             # First word of entity, if any
 *             if this.entity_is_open():             # <<<<<<<<<<<<<<
 *                 ids[1] = this.E(0)
 *             else:
 */
    __pyx_t_1 = (this->entity_is_open() != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":80
 *             # First word of entity, if any
 *             if this.entity_is_open():
 *                 ids[1] = this.E(0)             # <<<<<<<<<<<<<<
 *             else:
 *                 ids[1] = -1
 */
      (__pyx_v_ids[1]) = this->E(0);

      /* "spacy/pipeline/_parser_internals/_state.pxd":79
 *                 ids[0] = -1
 *             # First word of entity, if any
 *             if this.entity_is_open():             # <<<<<<<<<<<<<<
 *                 ids[1] = this.E(0)
 *             else:
 */
      goto __pyx_L5;
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":82
 *                 ids[1] = this.E(0)
 *             else:
 *                 ids[1] = -1             # <<<<<<<<<<<<<<
 *             # Last word of entity, if within entity
 *             if ids[0] == -1 or ids[1] == -1:
 */
    /*else*/ {
      (__pyx_v_ids[1]) = -1;
    }
    __pyx_L5:;

    /* "spacy/pipeline/_parser_internals/_state.pxd":84
 *                 ids[1] = -1
 *             # Last word of entity, if within entity
 *             if ids[0] == -1 or ids[1] == -1:             # <<<<<<<<<<<<<<
 *                 ids[2] = -1
 *             else:
 */
    __pyx_t_2 = (((__pyx_v_ids[0]) == -1L) != 0);
    if (!__pyx_t_2) {
    } else {
      __pyx_t_1 = __pyx_t_2;
      goto __pyx_L7_bool_binop_done;
    }
    __pyx_t_2 = (((__pyx_v_ids[1]) == -1L) != 0);
    __pyx_t_1 = __pyx_t_2;
    __pyx_L7_bool_binop_done:;
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":85
 *             # Last word of entity, if within entity
 *             if ids[0] == -1 or ids[1] == -1:
 *                 ids[2] = -1             # <<<<<<<<<<<<<<
 *             else:
 *                 ids[2] = ids[0] - 1
 */
      (__pyx_v_ids[2]) = -1;

      /* "spacy/pipeline/_parser_internals/_state.pxd":84
 *                 ids[1] = -1
 *             # Last word of entity, if within entity
 *             if ids[0] == -1 or ids[1] == -1:             # <<<<<<<<<<<<<<
 *                 ids[2] = -1
 *             else:
 */
      goto __pyx_L6;
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":87
 *                 ids[2] = -1
 *             else:
 *                 ids[2] = ids[0] - 1             # <<<<<<<<<<<<<<
 *         elif n == 8:
 *             ids[0] = this.B(0)
 */
    /*else*/ {
      (__pyx_v_ids[2]) = ((__pyx_v_ids[0]) - 1);
    }
    __pyx_L6:;

    /* "spacy/pipeline/_parser_internals/_state.pxd":73
 *             ids[0] = this.B(0)
 *             ids[1] = this.S(0)
 *         elif n == 3:             # <<<<<<<<<<<<<<
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)
 */
    break;
    case 8:

    /* "spacy/pipeline/_parser_internals/_state.pxd":89
 *                 ids[2] = ids[0] - 1
 *         elif n == 8:
 *             ids[0] = this.B(0)             # <<<<<<<<<<<<<<
 *             ids[1] = this.B(1)
 *             ids[2] = this.S(0)
 */
    (__pyx_v_ids[0]) = this->B(0);

    /* "spacy/pipeline/_parser_internals/_state.pxd":90
 *         elif n == 8:
 *             ids[0] = this.B(0)
 *             ids[1] = this.B(1)             # <<<<<<<<<<<<<<
 *             ids[2] = this.S(0)
 *             ids[3] = this.S(1)
 */
    (__pyx_v_ids[1]) = this->B(1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":91
 *             ids[0] = this.B(0)
 *             ids[1] = this.B(1)
 *             ids[2] = this.S(0)             # <<<<<<<<<<<<<<
 *             ids[3] = this.S(1)
 *             ids[4] = this.S(2)
 */
    (__pyx_v_ids[2]) = this->S(0);

    /* "spacy/pipeline/_parser_internals/_state.pxd":92
 *             ids[1] = this.B(1)
 *             ids[2] = this.S(0)
 *             ids[3] = this.S(1)             # <<<<<<<<<<<<<<
 *             ids[4] = this.S(2)
 *             ids[5] = this.L(this.B(0), 1)
 */
    (__pyx_v_ids[3]) = this->S(1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":93
 *             ids[2] = this.S(0)
 *             ids[3] = this.S(1)
 *             ids[4] = this.S(2)             # <<<<<<<<<<<<<<
 *             ids[5] = this.L(this.B(0), 1)
 *             ids[6] = this.L(this.S(0), 1)
 */
    (__pyx_v_ids[4]) = this->S(2);

    /* "spacy/pipeline/_parser_internals/_state.pxd":94
 *             ids[3] = this.S(1)
 *             ids[4] = this.S(2)
 *             ids[5] = this.L(this.B(0), 1)             # <<<<<<<<<<<<<<
 *             ids[6] = this.L(this.S(0), 1)
 *             ids[7] = this.R(this.S(0), 1)
 */
    (__pyx_v_ids[5]) = this->L(this->B(0), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":95
 *             ids[4] = this.S(2)
 *             ids[5] = this.L(this.B(0), 1)
 *             ids[6] = this.L(this.S(0), 1)             # <<<<<<<<<<<<<<
 *             ids[7] = this.R(this.S(0), 1)
 *         elif n == 13:
 */
    (__pyx_v_ids[6]) = this->L(this->S(0), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":96
 *             ids[5] = this.L(this.B(0), 1)
 *             ids[6] = this.L(this.S(0), 1)
 *             ids[7] = this.R(this.S(0), 1)             # <<<<<<<<<<<<<<
 *         elif n == 13:
 *             ids[0] = this.B(0)
 */
    (__pyx_v_ids[7]) = this->R(this->S(0), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":88
 *             else:
 *                 ids[2] = ids[0] - 1
 *         elif n == 8:             # <<<<<<<<<<<<<<
 *             ids[0] = this.B(0)
 *             ids[1] = this.B(1)
 */
    break;
    case 13:

    /* "spacy/pipeline/_parser_internals/_state.pxd":98
 *             ids[7] = this.R(this.S(0), 1)
 *         elif n == 13:
 *             ids[0] = this.B(0)             # <<<<<<<<<<<<<<
 *             ids[1] = this.B(1)
 *             ids[2] = this.S(0)
 */
    (__pyx_v_ids[0]) = this->B(0);

    /* "spacy/pipeline/_parser_internals/_state.pxd":99
 *         elif n == 13:
 *             ids[0] = this.B(0)
 *             ids[1] = this.B(1)             # <<<<<<<<<<<<<<
 *             ids[2] = this.S(0)
 *             ids[3] = this.S(1)
 */
    (__pyx_v_ids[1]) = this->B(1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":100
 *             ids[0] = this.B(0)
 *             ids[1] = this.B(1)
 *             ids[2] = this.S(0)             # <<<<<<<<<<<<<<
 *             ids[3] = this.S(1)
 *             ids[4] = this.S(2)
 */
    (__pyx_v_ids[2]) = this->S(0);

    /* "spacy/pipeline/_parser_internals/_state.pxd":101
 *             ids[1] = this.B(1)
 *             ids[2] = this.S(0)
 *             ids[3] = this.S(1)             # <<<<<<<<<<<<<<
 *             ids[4] = this.S(2)
 *             ids[5] = this.L(this.S(0), 1)
 */
    (__pyx_v_ids[3]) = this->S(1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":102
 *             ids[2] = this.S(0)
 *             ids[3] = this.S(1)
 *             ids[4] = this.S(2)             # <<<<<<<<<<<<<<
 *             ids[5] = this.L(this.S(0), 1)
 *             ids[6] = this.L(this.S(0), 2)
 */
    (__pyx_v_ids[4]) = this->S(2);

    /* "spacy/pipeline/_parser_internals/_state.pxd":103
 *             ids[3] = this.S(1)
 *             ids[4] = this.S(2)
 *             ids[5] = this.L(this.S(0), 1)             # <<<<<<<<<<<<<<
 *             ids[6] = this.L(this.S(0), 2)
 *             ids[6] = this.R(this.S(0), 1)
 */
    (__pyx_v_ids[5]) = this->L(this->S(0), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":104
 *             ids[4] = this.S(2)
 *             ids[5] = this.L(this.S(0), 1)
 *             ids[6] = this.L(this.S(0), 2)             # <<<<<<<<<<<<<<
 *             ids[6] = this.R(this.S(0), 1)
 *             ids[7] = this.L(this.B(0), 1)
 */
    (__pyx_v_ids[6]) = this->L(this->S(0), 2);

    /* "spacy/pipeline/_parser_internals/_state.pxd":105
 *             ids[5] = this.L(this.S(0), 1)
 *             ids[6] = this.L(this.S(0), 2)
 *             ids[6] = this.R(this.S(0), 1)             # <<<<<<<<<<<<<<
 *             ids[7] = this.L(this.B(0), 1)
 *             ids[8] = this.R(this.S(0), 2)
 */
    (__pyx_v_ids[6]) = this->R(this->S(0), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":106
 *             ids[6] = this.L(this.S(0), 2)
 *             ids[6] = this.R(this.S(0), 1)
 *             ids[7] = this.L(this.B(0), 1)             # <<<<<<<<<<<<<<
 *             ids[8] = this.R(this.S(0), 2)
 *             ids[9] = this.L(this.S(1), 1)
 */
    (__pyx_v_ids[7]) = this->L(this->B(0), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":107
 *             ids[6] = this.R(this.S(0), 1)
 *             ids[7] = this.L(this.B(0), 1)
 *             ids[8] = this.R(this.S(0), 2)             # <<<<<<<<<<<<<<
 *             ids[9] = this.L(this.S(1), 1)
 *             ids[10] = this.L(this.S(1), 2)
 */
    (__pyx_v_ids[8]) = this->R(this->S(0), 2);

    /* "spacy/pipeline/_parser_internals/_state.pxd":108
 *             ids[7] = this.L(this.B(0), 1)
 *             ids[8] = this.R(this.S(0), 2)
 *             ids[9] = this.L(this.S(1), 1)             # <<<<<<<<<<<<<<
 *             ids[10] = this.L(this.S(1), 2)
 *             ids[11] = this.R(this.S(1), 1)
 */
    (__pyx_v_ids[9]) = this->L(this->S(1), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":109
 *             ids[8] = this.R(this.S(0), 2)
 *             ids[9] = this.L(this.S(1), 1)
 *             ids[10] = this.L(this.S(1), 2)             # <<<<<<<<<<<<<<
 *             ids[11] = this.R(this.S(1), 1)
 *             ids[12] = this.R(this.S(1), 2)
 */
    (__pyx_v_ids[10]) = this->L(this->S(1), 2);

    /* "spacy/pipeline/_parser_internals/_state.pxd":110
 *             ids[9] = this.L(this.S(1), 1)
 *             ids[10] = this.L(this.S(1), 2)
 *             ids[11] = this.R(this.S(1), 1)             # <<<<<<<<<<<<<<
 *             ids[12] = this.R(this.S(1), 2)
 *         elif n == 6:
 */
    (__pyx_v_ids[11]) = this->R(this->S(1), 1);

    /* "spacy/pipeline/_parser_internals/_state.pxd":111
 *             ids[10] = this.L(this.S(1), 2)
 *             ids[11] = this.R(this.S(1), 1)
 *             ids[12] = this.R(this.S(1), 2)             # <<<<<<<<<<<<<<
 *         elif n == 6:
 *             for i in range(6):
 */
    (__pyx_v_ids[12]) = this->R(this->S(1), 2);

    /* "spacy/pipeline/_parser_internals/_state.pxd":97
 *             ids[6] = this.L(this.S(0), 1)
 *             ids[7] = this.R(this.S(0), 1)
 *         elif n == 13:             # <<<<<<<<<<<<<<
 *             ids[0] = this.B(0)
 *             ids[1] = this.B(1)
 */
    break;
    case 6:

    /* "spacy/pipeline/_parser_internals/_state.pxd":113
 *             ids[12] = this.R(this.S(1), 2)
 *         elif n == 6:
 *             for i in range(6):             # <<<<<<<<<<<<<<
 *                 ids[i] = -1
 *             if this.B(0) >= 0:
 */
    for (__pyx_t_3 = 0; __pyx_t_3 < 6; __pyx_t_3+=1) {
      __pyx_v_i = __pyx_t_3;

      /* "spacy/pipeline/_parser_internals/_state.pxd":114
 *         elif n == 6:
 *             for i in range(6):
 *                 ids[i] = -1             # <<<<<<<<<<<<<<
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)
 */
      (__pyx_v_ids[__pyx_v_i]) = -1;
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":115
 *             for i in range(6):
 *                 ids[i] = -1
 *             if this.B(0) >= 0:             # <<<<<<<<<<<<<<
 *                 ids[0] = this.B(0)
 *             if this.entity_is_open():
 */
    __pyx_t_1 = ((this->B(0) >= 0) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":116
 *                 ids[i] = -1
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)             # <<<<<<<<<<<<<<
 *             if this.entity_is_open():
 *                 ent = this.get_ent()
 */
      (__pyx_v_ids[0]) = this->B(0);

      /* "spacy/pipeline/_parser_internals/_state.pxd":115
 *             for i in range(6):
 *                 ids[i] = -1
 *             if this.B(0) >= 0:             # <<<<<<<<<<<<<<
 *                 ids[0] = this.B(0)
 *             if this.entity_is_open():
 */
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":117
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)
 *             if this.entity_is_open():             # <<<<<<<<<<<<<<
 *                 ent = this.get_ent()
 *                 j = 1
 */
    __pyx_t_1 = (this->entity_is_open() != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":118
 *                 ids[0] = this.B(0)
 *             if this.entity_is_open():
 *                 ent = this.get_ent()             # <<<<<<<<<<<<<<
 *                 j = 1
 *                 for i in range(ent.start, this.B(0)):
 */
      __pyx_v_ent = this->get_ent();

      /* "spacy/pipeline/_parser_internals/_state.pxd":119
 *             if this.entity_is_open():
 *                 ent = this.get_ent()
 *                 j = 1             # <<<<<<<<<<<<<<
 *                 for i in range(ent.start, this.B(0)):
 *                     ids[j] = i
 */
      __pyx_v_j = 1;

      /* "spacy/pipeline/_parser_internals/_state.pxd":120
 *                 ent = this.get_ent()
 *                 j = 1
 *                 for i in range(ent.start, this.B(0)):             # <<<<<<<<<<<<<<
 *                     ids[j] = i
 *                     j += 1
 */
      __pyx_t_3 = this->B(0);
      __pyx_t_4 = __pyx_t_3;
      for (__pyx_t_5 = __pyx_v_ent.start; __pyx_t_5 < __pyx_t_4; __pyx_t_5+=1) {
        __pyx_v_i = __pyx_t_5;

        /* "spacy/pipeline/_parser_internals/_state.pxd":121
 *                 j = 1
 *                 for i in range(ent.start, this.B(0)):
 *                     ids[j] = i             # <<<<<<<<<<<<<<
 *                     j += 1
 *                     if j >= 6:
 */
        (__pyx_v_ids[__pyx_v_j]) = __pyx_v_i;

        /* "spacy/pipeline/_parser_internals/_state.pxd":122
 *                 for i in range(ent.start, this.B(0)):
 *                     ids[j] = i
 *                     j += 1             # <<<<<<<<<<<<<<
 *                     if j >= 6:
 *                         break
 */
        __pyx_v_j = (__pyx_v_j + 1);

        /* "spacy/pipeline/_parser_internals/_state.pxd":123
 *                     ids[j] = i
 *                     j += 1
 *                     if j >= 6:             # <<<<<<<<<<<<<<
 *                         break
 *         else:
 */
        __pyx_t_1 = ((__pyx_v_j >= 6) != 0);
        if (__pyx_t_1) {

          /* "spacy/pipeline/_parser_internals/_state.pxd":124
 *                     j += 1
 *                     if j >= 6:
 *                         break             # <<<<<<<<<<<<<<
 *         else:
 *             # TODO error =/
 */
          goto __pyx_L14_break;

          /* "spacy/pipeline/_parser_internals/_state.pxd":123
 *                     ids[j] = i
 *                     j += 1
 *                     if j >= 6:             # <<<<<<<<<<<<<<
 *                         break
 *         else:
 */
        }
      }
      __pyx_L14_break:;

      /* "spacy/pipeline/_parser_internals/_state.pxd":117
 *             if this.B(0) >= 0:
 *                 ids[0] = this.B(0)
 *             if this.entity_is_open():             # <<<<<<<<<<<<<<
 *                 ent = this.get_ent()
 *                 j = 1
 */
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":112
 *             ids[11] = this.R(this.S(1), 1)
 *             ids[12] = this.R(this.S(1), 2)
 *         elif n == 6:             # <<<<<<<<<<<<<<
 *             for i in range(6):
 *                 ids[i] = -1
 */
    break;
    default:
    break;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":128
 *             # TODO error =/
 *             pass
 *         for i in range(n):             # <<<<<<<<<<<<<<
 *             if ids[i] >= 0:
 *                 ids[i] += this.offset
 */
  __pyx_t_3 = __pyx_v_n;
  __pyx_t_4 = __pyx_t_3;
  for (__pyx_t_5 = 0; __pyx_t_5 < __pyx_t_4; __pyx_t_5+=1) {
    __pyx_v_i = __pyx_t_5;

    /* "spacy/pipeline/_parser_internals/_state.pxd":129
 *             pass
 *         for i in range(n):
 *             if ids[i] >= 0:             # <<<<<<<<<<<<<<
 *                 ids[i] += this.offset
 *             else:
 */
    __pyx_t_1 = (((__pyx_v_ids[__pyx_v_i]) >= 0) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":130
 *         for i in range(n):
 *             if ids[i] >= 0:
 *                 ids[i] += this.offset             # <<<<<<<<<<<<<<
 *             else:
 *                 ids[i] = -1
 */
      __pyx_t_6 = __pyx_v_i;
      (__pyx_v_ids[__pyx_t_6]) = ((__pyx_v_ids[__pyx_t_6]) + this->offset);

      /* "spacy/pipeline/_parser_internals/_state.pxd":129
 *             pass
 *         for i in range(n):
 *             if ids[i] >= 0:             # <<<<<<<<<<<<<<
 *                 ids[i] += this.offset
 *             else:
 */
      goto __pyx_L18;
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":132
 *                 ids[i] += this.offset
 *             else:
 *                 ids[i] = -1             # <<<<<<<<<<<<<<
 * 
 *     int S(int i) nogil const:
 */
    /*else*/ {
      (__pyx_v_ids[__pyx_v_i]) = -1;
    }
    __pyx_L18:;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":63
 *         free(this._heads)
 * 
 *     void set_context_tokens(int* ids, int n) nogil:             # <<<<<<<<<<<<<<
 *         cdef int i, j
 *         if n == 1:
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":134
 *                 ids[i] = -1
 * 
 *     int S(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if i >= this._stack.size():
 *             return -1
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::S(int __pyx_v_i) const {
  int __pyx_r;
  int __pyx_t_1;
  __Pyx_FakeReference<int> __pyx_t_2;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":135
 * 
 *     int S(int i) nogil const:
 *         if i >= this._stack.size():             # <<<<<<<<<<<<<<
 *             return -1
 *         elif i < 0:
 */
  __pyx_t_1 = ((__pyx_v_i >= this->_stack.size()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":136
 *     int S(int i) nogil const:
 *         if i >= this._stack.size():
 *             return -1             # <<<<<<<<<<<<<<
 *         elif i < 0:
 *             return -1
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":135
 * 
 *     int S(int i) nogil const:
 *         if i >= this._stack.size():             # <<<<<<<<<<<<<<
 *             return -1
 *         elif i < 0:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":137
 *         if i >= this._stack.size():
 *             return -1
 *         elif i < 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         return this._stack.at(this._stack.size() - (i+1))
 */
  __pyx_t_1 = ((__pyx_v_i < 0) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":138
 *             return -1
 *         elif i < 0:
 *             return -1             # <<<<<<<<<<<<<<
 *         return this._stack.at(this._stack.size() - (i+1))
 * 
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":137
 *         if i >= this._stack.size():
 *             return -1
 *         elif i < 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         return this._stack.at(this._stack.size() - (i+1))
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":139
 *         elif i < 0:
 *             return -1
 *         return this._stack.at(this._stack.size() - (i+1))             # <<<<<<<<<<<<<<
 * 
 *     int B(int i) nogil const:
 */
  try {
    __pyx_t_2 = this->_stack.at((this->_stack.size() - (__pyx_v_i + 1)));
  } catch(...) {
    #ifdef WITH_THREAD
    PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
    #endif
    __Pyx_CppExn2PyErr();
    #ifdef WITH_THREAD
    __Pyx_PyGILState_Release(__pyx_gilstate_save);
    #endif
    __PYX_ERR(0, 139, __pyx_L1_error)
  }
  __pyx_r = __pyx_t_2;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":134
 *                 ids[i] = -1
 * 
 *     int S(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if i >= this._stack.size():
 *             return -1
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.S", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_r = 0;
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":141
 *         return this._stack.at(this._stack.size() - (i+1))
 * 
 *     int B(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if i < 0:
 *             return -1
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::B(int __pyx_v_i) const {
  std::vector<int> ::size_type __pyx_v_b_i;
  int __pyx_r;
  int __pyx_t_1;
  __Pyx_FakeReference<int> __pyx_t_2;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":142
 * 
 *     int B(int i) nogil const:
 *         if i < 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         elif i < this._rebuffer.size():
 */
  __pyx_t_1 = ((__pyx_v_i < 0) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":143
 *     int B(int i) nogil const:
 *         if i < 0:
 *             return -1             # <<<<<<<<<<<<<<
 *         elif i < this._rebuffer.size():
 *             return this._rebuffer.at(this._rebuffer.size() - (i+1))
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":142
 * 
 *     int B(int i) nogil const:
 *         if i < 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         elif i < this._rebuffer.size():
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":144
 *         if i < 0:
 *             return -1
 *         elif i < this._rebuffer.size():             # <<<<<<<<<<<<<<
 *             return this._rebuffer.at(this._rebuffer.size() - (i+1))
 *         else:
 */
  __pyx_t_1 = ((__pyx_v_i < this->_rebuffer.size()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":145
 *             return -1
 *         elif i < this._rebuffer.size():
 *             return this._rebuffer.at(this._rebuffer.size() - (i+1))             # <<<<<<<<<<<<<<
 *         else:
 *             b_i = this._b_i + (i - this._rebuffer.size())
 */
    try {
      __pyx_t_2 = this->_rebuffer.at((this->_rebuffer.size() - (__pyx_v_i + 1)));
    } catch(...) {
      #ifdef WITH_THREAD
      PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      __Pyx_CppExn2PyErr();
      #ifdef WITH_THREAD
      __Pyx_PyGILState_Release(__pyx_gilstate_save);
      #endif
      __PYX_ERR(0, 145, __pyx_L1_error)
    }
    __pyx_r = __pyx_t_2;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":144
 *         if i < 0:
 *             return -1
 *         elif i < this._rebuffer.size():             # <<<<<<<<<<<<<<
 *             return this._rebuffer.at(this._rebuffer.size() - (i+1))
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":147
 *             return this._rebuffer.at(this._rebuffer.size() - (i+1))
 *         else:
 *             b_i = this._b_i + (i - this._rebuffer.size())             # <<<<<<<<<<<<<<
 *             if b_i >= this.length:
 *                 return -1
 */
  /*else*/ {
    __pyx_v_b_i = (this->_b_i + (__pyx_v_i - this->_rebuffer.size()));

    /* "spacy/pipeline/_parser_internals/_state.pxd":148
 *         else:
 *             b_i = this._b_i + (i - this._rebuffer.size())
 *             if b_i >= this.length:             # <<<<<<<<<<<<<<
 *                 return -1
 *             else:
 */
    __pyx_t_1 = ((__pyx_v_b_i >= this->length) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":149
 *             b_i = this._b_i + (i - this._rebuffer.size())
 *             if b_i >= this.length:
 *                 return -1             # <<<<<<<<<<<<<<
 *             else:
 *                 return b_i
 */
      __pyx_r = -1;
      goto __pyx_L0;

      /* "spacy/pipeline/_parser_internals/_state.pxd":148
 *         else:
 *             b_i = this._b_i + (i - this._rebuffer.size())
 *             if b_i >= this.length:             # <<<<<<<<<<<<<<
 *                 return -1
 *             else:
 */
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":151
 *                 return -1
 *             else:
 *                 return b_i             # <<<<<<<<<<<<<<
 * 
 *     const TokenC* B_(int i) nogil const:
 */
    /*else*/ {
      __pyx_r = __pyx_v_b_i;
      goto __pyx_L0;
    }
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":141
 *         return this._stack.at(this._stack.size() - (i+1))
 * 
 *     int B(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if i < 0:
 *             return -1
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.B", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_r = 0;
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":153
 *                 return b_i
 * 
 *     const TokenC* B_(int i) nogil const:             # <<<<<<<<<<<<<<
 *         return this.safe_get(this.B(i))
 * 
 */

struct __pyx_t_5spacy_7structs_TokenC const *__pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::B_(int __pyx_v_i) const {
  struct __pyx_t_5spacy_7structs_TokenC const *__pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":154
 * 
 *     const TokenC* B_(int i) nogil const:
 *         return this.safe_get(this.B(i))             # <<<<<<<<<<<<<<
 * 
 *     const TokenC* E_(int i) nogil const:
 */
  __pyx_r = this->safe_get(this->B(__pyx_v_i));
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":153
 *                 return b_i
 * 
 *     const TokenC* B_(int i) nogil const:             # <<<<<<<<<<<<<<
 *         return this.safe_get(this.B(i))
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":156
 *         return this.safe_get(this.B(i))
 * 
 *     const TokenC* E_(int i) nogil const:             # <<<<<<<<<<<<<<
 *         return this.safe_get(this.E(i))
 * 
 */

struct __pyx_t_5spacy_7structs_TokenC const *__pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::E_(int __pyx_v_i) const {
  struct __pyx_t_5spacy_7structs_TokenC const *__pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":157
 * 
 *     const TokenC* E_(int i) nogil const:
 *         return this.safe_get(this.E(i))             # <<<<<<<<<<<<<<
 * 
 *     const TokenC* safe_get(int i) nogil const:
 */
  __pyx_r = this->safe_get(this->E(__pyx_v_i));
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":156
 *         return this.safe_get(this.B(i))
 * 
 *     const TokenC* E_(int i) nogil const:             # <<<<<<<<<<<<<<
 *         return this.safe_get(this.E(i))
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":159
 *         return this.safe_get(this.E(i))
 * 
 *     const TokenC* safe_get(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if i < 0 or i >= this.length:
 *             return &this._empty_token
 */

struct __pyx_t_5spacy_7structs_TokenC const *__pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::safe_get(int __pyx_v_i) const {
  struct __pyx_t_5spacy_7structs_TokenC const *__pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":160
 * 
 *     const TokenC* safe_get(int i) nogil const:
 *         if i < 0 or i >= this.length:             # <<<<<<<<<<<<<<
 *             return &this._empty_token
 *         else:
 */
  __pyx_t_2 = ((__pyx_v_i < 0) != 0);
  if (!__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_2 = ((__pyx_v_i >= this->length) != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":161
 *     const TokenC* safe_get(int i) nogil const:
 *         if i < 0 or i >= this.length:
 *             return &this._empty_token             # <<<<<<<<<<<<<<
 *         else:
 *             return &this._sent[i]
 */
    __pyx_r = (&this->_empty_token);
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":160
 * 
 *     const TokenC* safe_get(int i) nogil const:
 *         if i < 0 or i >= this.length:             # <<<<<<<<<<<<<<
 *             return &this._empty_token
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":163
 *             return &this._empty_token
 *         else:
 *             return &this._sent[i]             # <<<<<<<<<<<<<<
 * 
 *     void map_get_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, vector[ArcC]* out) nogil const:
 */
  /*else*/ {
    __pyx_r = (&(this->_sent[__pyx_v_i]));
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":159
 *         return this.safe_get(this.E(i))
 * 
 *     const TokenC* safe_get(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if i < 0 or i >= this.length:
 *             return &this._empty_token
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":165
 *             return &this._sent[i]
 * 
 *     void map_get_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, vector[ArcC]* out) nogil const:             # <<<<<<<<<<<<<<
 *         cdef const vector[ArcC]* arcs
 *         head_arcs_it = heads_arcs.const_begin()
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::map_get_arcs(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  const &__pyx_v_heads_arcs, std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  *__pyx_v_out) const {
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  const *__pyx_v_arcs;
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> > ::const_iterator __pyx_v_head_arcs_it;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::const_iterator __pyx_v_arcs_it;
  struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC __pyx_v_arc;
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":167
 *     void map_get_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, vector[ArcC]* out) nogil const:
 *         cdef const vector[ArcC]* arcs
 *         head_arcs_it = heads_arcs.const_begin()             # <<<<<<<<<<<<<<
 *         while head_arcs_it != heads_arcs.const_end():
 *             arcs = &deref(head_arcs_it).second
 */
  __pyx_v_head_arcs_it = __pyx_v_heads_arcs.begin();

  /* "spacy/pipeline/_parser_internals/_state.pxd":168
 *         cdef const vector[ArcC]* arcs
 *         head_arcs_it = heads_arcs.const_begin()
 *         while head_arcs_it != heads_arcs.const_end():             # <<<<<<<<<<<<<<
 *             arcs = &deref(head_arcs_it).second
 *             arcs_it = arcs.const_begin()
 */
  while (1) {
    __pyx_t_1 = ((__pyx_v_head_arcs_it != __pyx_v_heads_arcs.end()) != 0);
    if (!__pyx_t_1) break;

    /* "spacy/pipeline/_parser_internals/_state.pxd":169
 *         head_arcs_it = heads_arcs.const_begin()
 *         while head_arcs_it != heads_arcs.const_end():
 *             arcs = &deref(head_arcs_it).second             # <<<<<<<<<<<<<<
 *             arcs_it = arcs.const_begin()
 *             while arcs_it != arcs.const_end():
 */
    __pyx_v_arcs = (&(*__pyx_v_head_arcs_it).second);

    /* "spacy/pipeline/_parser_internals/_state.pxd":170
 *         while head_arcs_it != heads_arcs.const_end():
 *             arcs = &deref(head_arcs_it).second
 *             arcs_it = arcs.const_begin()             # <<<<<<<<<<<<<<
 *             while arcs_it != arcs.const_end():
 *                 arc = deref(arcs_it)
 */
    __pyx_v_arcs_it = __pyx_v_arcs->begin();

    /* "spacy/pipeline/_parser_internals/_state.pxd":171
 *             arcs = &deref(head_arcs_it).second
 *             arcs_it = arcs.const_begin()
 *             while arcs_it != arcs.const_end():             # <<<<<<<<<<<<<<
 *                 arc = deref(arcs_it)
 *                 if arc.head != -1 and arc.child != -1:
 */
    while (1) {
      __pyx_t_1 = ((__pyx_v_arcs_it != __pyx_v_arcs->end()) != 0);
      if (!__pyx_t_1) break;

      /* "spacy/pipeline/_parser_internals/_state.pxd":172
 *             arcs_it = arcs.const_begin()
 *             while arcs_it != arcs.const_end():
 *                 arc = deref(arcs_it)             # <<<<<<<<<<<<<<
 *                 if arc.head != -1 and arc.child != -1:
 *                     out.push_back(arc)
 */
      __pyx_v_arc = (*__pyx_v_arcs_it);

      /* "spacy/pipeline/_parser_internals/_state.pxd":173
 *             while arcs_it != arcs.const_end():
 *                 arc = deref(arcs_it)
 *                 if arc.head != -1 and arc.child != -1:             # <<<<<<<<<<<<<<
 *                     out.push_back(arc)
 *                 incr(arcs_it)
 */
      __pyx_t_2 = ((__pyx_v_arc.head != -1L) != 0);
      if (__pyx_t_2) {
      } else {
        __pyx_t_1 = __pyx_t_2;
        goto __pyx_L8_bool_binop_done;
      }
      __pyx_t_2 = ((__pyx_v_arc.child != -1L) != 0);
      __pyx_t_1 = __pyx_t_2;
      __pyx_L8_bool_binop_done:;
      if (__pyx_t_1) {

        /* "spacy/pipeline/_parser_internals/_state.pxd":174
 *                 arc = deref(arcs_it)
 *                 if arc.head != -1 and arc.child != -1:
 *                     out.push_back(arc)             # <<<<<<<<<<<<<<
 *                 incr(arcs_it)
 *             incr(head_arcs_it)
 */
        try {
          __pyx_v_out->push_back(__pyx_v_arc);
        } catch(...) {
          #ifdef WITH_THREAD
          PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
          #endif
          __Pyx_CppExn2PyErr();
          #ifdef WITH_THREAD
          __Pyx_PyGILState_Release(__pyx_gilstate_save);
          #endif
          __PYX_ERR(0, 174, __pyx_L1_error)
        }

        /* "spacy/pipeline/_parser_internals/_state.pxd":173
 *             while arcs_it != arcs.const_end():
 *                 arc = deref(arcs_it)
 *                 if arc.head != -1 and arc.child != -1:             # <<<<<<<<<<<<<<
 *                     out.push_back(arc)
 *                 incr(arcs_it)
 */
      }

      /* "spacy/pipeline/_parser_internals/_state.pxd":175
 *                 if arc.head != -1 and arc.child != -1:
 *                     out.push_back(arc)
 *                 incr(arcs_it)             # <<<<<<<<<<<<<<
 *             incr(head_arcs_it)
 * 
 */
      (void)((++__pyx_v_arcs_it));
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":176
 *                     out.push_back(arc)
 *                 incr(arcs_it)
 *             incr(head_arcs_it)             # <<<<<<<<<<<<<<
 * 
 *     void get_arcs(vector[ArcC]* out) nogil const:
 */
    (void)((++__pyx_v_head_arcs_it));
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":165
 *             return &this._sent[i]
 * 
 *     void map_get_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, vector[ArcC]* out) nogil const:             # <<<<<<<<<<<<<<
 *         cdef const vector[ArcC]* arcs
 *         head_arcs_it = heads_arcs.const_begin()
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.map_get_arcs", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":178
 *             incr(head_arcs_it)
 * 
 *     void get_arcs(vector[ArcC]* out) nogil const:             # <<<<<<<<<<<<<<
 *         this.map_get_arcs(this._left_arcs, out)
 *         this.map_get_arcs(this._right_arcs, out)
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::get_arcs(std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  *__pyx_v_out) const {

  /* "spacy/pipeline/_parser_internals/_state.pxd":179
 * 
 *     void get_arcs(vector[ArcC]* out) nogil const:
 *         this.map_get_arcs(this._left_arcs, out)             # <<<<<<<<<<<<<<
 *         this.map_get_arcs(this._right_arcs, out)
 * 
 */
  this->map_get_arcs(this->_left_arcs, __pyx_v_out);

  /* "spacy/pipeline/_parser_internals/_state.pxd":180
 *     void get_arcs(vector[ArcC]* out) nogil const:
 *         this.map_get_arcs(this._left_arcs, out)
 *         this.map_get_arcs(this._right_arcs, out)             # <<<<<<<<<<<<<<
 * 
 *     int H(int child) nogil const:
 */
  this->map_get_arcs(this->_right_arcs, __pyx_v_out);

  /* "spacy/pipeline/_parser_internals/_state.pxd":178
 *             incr(head_arcs_it)
 * 
 *     void get_arcs(vector[ArcC]* out) nogil const:             # <<<<<<<<<<<<<<
 *         this.map_get_arcs(this._left_arcs, out)
 *         this.map_get_arcs(this._right_arcs, out)
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":182
 *         this.map_get_arcs(this._right_arcs, out)
 * 
 *     int H(int child) nogil const:             # <<<<<<<<<<<<<<
 *         if child >= this.length or child < 0:
 *             return -1
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::H(int __pyx_v_child) const {
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":183
 * 
 *     int H(int child) nogil const:
 *         if child >= this.length or child < 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         else:
 */
  __pyx_t_2 = ((__pyx_v_child >= this->length) != 0);
  if (!__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_2 = ((__pyx_v_child < 0) != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":184
 *     int H(int child) nogil const:
 *         if child >= this.length or child < 0:
 *             return -1             # <<<<<<<<<<<<<<
 *         else:
 *             return this._heads[child]
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":183
 * 
 *     int H(int child) nogil const:
 *         if child >= this.length or child < 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":186
 *             return -1
 *         else:
 *             return this._heads[child]             # <<<<<<<<<<<<<<
 * 
 *     int E(int i) nogil const:
 */
  /*else*/ {
    __pyx_r = (this->_heads[__pyx_v_child]);
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":182
 *         this.map_get_arcs(this._right_arcs, out)
 * 
 *     int H(int child) nogil const:             # <<<<<<<<<<<<<<
 *         if child >= this.length or child < 0:
 *             return -1
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":188
 *             return this._heads[child]
 * 
 *     int E(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if this._ents.size() == 0:
 *             return -1
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::E(CYTHON_UNUSED int __pyx_v_i) const {
  int __pyx_r;
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":189
 * 
 *     int E(int i) nogil const:
 *         if this._ents.size() == 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         else:
 */
  __pyx_t_1 = ((this->_ents.size() == 0) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":190
 *     int E(int i) nogil const:
 *         if this._ents.size() == 0:
 *             return -1             # <<<<<<<<<<<<<<
 *         else:
 *             return this._ents.back().start
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":189
 * 
 *     int E(int i) nogil const:
 *         if this._ents.size() == 0:             # <<<<<<<<<<<<<<
 *             return -1
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":192
 *             return -1
 *         else:
 *             return this._ents.back().start             # <<<<<<<<<<<<<<
 * 
 *     int nth_child(const unordered_map[int, vector[ArcC]]& heads_arcs, int head, int idx) nogil const:
 */
  /*else*/ {
    __pyx_r = this->_ents.back().start;
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":188
 *             return this._heads[child]
 * 
 *     int E(int i) nogil const:             # <<<<<<<<<<<<<<
 *         if this._ents.size() == 0:
 *             return -1
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":194
 *             return this._ents.back().start
 * 
 *     int nth_child(const unordered_map[int, vector[ArcC]]& heads_arcs, int head, int idx) nogil const:             # <<<<<<<<<<<<<<
 *         if idx < 1:
 *             return -1
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::nth_child(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  const &__pyx_v_heads_arcs, int __pyx_v_head, int __pyx_v_idx) const {
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> > ::const_iterator __pyx_v_head_arcs_it;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  const *__pyx_v_arcs;
  size_t __pyx_v_child_index;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::const_reverse_iterator __pyx_v_arcs_it;
  struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC __pyx_v_arc;
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":195
 * 
 *     int nth_child(const unordered_map[int, vector[ArcC]]& heads_arcs, int head, int idx) nogil const:
 *         if idx < 1:             # <<<<<<<<<<<<<<
 *             return -1
 * 
 */
  __pyx_t_1 = ((__pyx_v_idx < 1) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":196
 *     int nth_child(const unordered_map[int, vector[ArcC]]& heads_arcs, int head, int idx) nogil const:
 *         if idx < 1:
 *             return -1             # <<<<<<<<<<<<<<
 * 
 *         head_arcs_it = heads_arcs.const_find(head)
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":195
 * 
 *     int nth_child(const unordered_map[int, vector[ArcC]]& heads_arcs, int head, int idx) nogil const:
 *         if idx < 1:             # <<<<<<<<<<<<<<
 *             return -1
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":198
 *             return -1
 * 
 *         head_arcs_it = heads_arcs.const_find(head)             # <<<<<<<<<<<<<<
 *         if head_arcs_it == heads_arcs.const_end():
 *             return -1
 */
  __pyx_v_head_arcs_it = __pyx_v_heads_arcs.find(__pyx_v_head);

  /* "spacy/pipeline/_parser_internals/_state.pxd":199
 * 
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():             # <<<<<<<<<<<<<<
 *             return -1
 * 
 */
  __pyx_t_1 = ((__pyx_v_head_arcs_it == __pyx_v_heads_arcs.end()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":200
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():
 *             return -1             # <<<<<<<<<<<<<<
 * 
 *         cdef const vector[ArcC]* arcs = &deref(head_arcs_it).second
 */
    __pyx_r = -1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":199
 * 
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():             # <<<<<<<<<<<<<<
 *             return -1
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":202
 *             return -1
 * 
 *         cdef const vector[ArcC]* arcs = &deref(head_arcs_it).second             # <<<<<<<<<<<<<<
 * 
 *         # Work backwards through arcs to find the arc at the
 */
  __pyx_v_arcs = (&(*__pyx_v_head_arcs_it).second);

  /* "spacy/pipeline/_parser_internals/_state.pxd":206
 *         # Work backwards through arcs to find the arc at the
 *         # requested index more quickly.
 *         cdef size_t child_index = 0             # <<<<<<<<<<<<<<
 *         arcs_it = arcs.const_rbegin()
 *         while arcs_it != arcs.const_rend() and child_index != idx:
 */
  __pyx_v_child_index = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":207
 *         # requested index more quickly.
 *         cdef size_t child_index = 0
 *         arcs_it = arcs.const_rbegin()             # <<<<<<<<<<<<<<
 *         while arcs_it != arcs.const_rend() and child_index != idx:
 *             arc = deref(arcs_it)
 */
  __pyx_v_arcs_it = __pyx_v_arcs->crbegin();

  /* "spacy/pipeline/_parser_internals/_state.pxd":208
 *         cdef size_t child_index = 0
 *         arcs_it = arcs.const_rbegin()
 *         while arcs_it != arcs.const_rend() and child_index != idx:             # <<<<<<<<<<<<<<
 *             arc = deref(arcs_it)
 *             if arc.child != -1:
 */
  while (1) {
    __pyx_t_2 = ((__pyx_v_arcs_it != __pyx_v_arcs->crend()) != 0);
    if (__pyx_t_2) {
    } else {
      __pyx_t_1 = __pyx_t_2;
      goto __pyx_L7_bool_binop_done;
    }
    __pyx_t_2 = ((__pyx_v_child_index != __pyx_v_idx) != 0);
    __pyx_t_1 = __pyx_t_2;
    __pyx_L7_bool_binop_done:;
    if (!__pyx_t_1) break;

    /* "spacy/pipeline/_parser_internals/_state.pxd":209
 *         arcs_it = arcs.const_rbegin()
 *         while arcs_it != arcs.const_rend() and child_index != idx:
 *             arc = deref(arcs_it)             # <<<<<<<<<<<<<<
 *             if arc.child != -1:
 *                 child_index += 1
 */
    __pyx_v_arc = (*__pyx_v_arcs_it);

    /* "spacy/pipeline/_parser_internals/_state.pxd":210
 *         while arcs_it != arcs.const_rend() and child_index != idx:
 *             arc = deref(arcs_it)
 *             if arc.child != -1:             # <<<<<<<<<<<<<<
 *                 child_index += 1
 *                 if child_index == idx:
 */
    __pyx_t_1 = ((__pyx_v_arc.child != -1L) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":211
 *             arc = deref(arcs_it)
 *             if arc.child != -1:
 *                 child_index += 1             # <<<<<<<<<<<<<<
 *                 if child_index == idx:
 *                     return arc.child
 */
      __pyx_v_child_index = (__pyx_v_child_index + 1);

      /* "spacy/pipeline/_parser_internals/_state.pxd":212
 *             if arc.child != -1:
 *                 child_index += 1
 *                 if child_index == idx:             # <<<<<<<<<<<<<<
 *                     return arc.child
 *             incr(arcs_it)
 */
      __pyx_t_1 = ((__pyx_v_child_index == __pyx_v_idx) != 0);
      if (__pyx_t_1) {

        /* "spacy/pipeline/_parser_internals/_state.pxd":213
 *                 child_index += 1
 *                 if child_index == idx:
 *                     return arc.child             # <<<<<<<<<<<<<<
 *             incr(arcs_it)
 * 
 */
        __pyx_r = __pyx_v_arc.child;
        goto __pyx_L0;

        /* "spacy/pipeline/_parser_internals/_state.pxd":212
 *             if arc.child != -1:
 *                 child_index += 1
 *                 if child_index == idx:             # <<<<<<<<<<<<<<
 *                     return arc.child
 *             incr(arcs_it)
 */
      }

      /* "spacy/pipeline/_parser_internals/_state.pxd":210
 *         while arcs_it != arcs.const_rend() and child_index != idx:
 *             arc = deref(arcs_it)
 *             if arc.child != -1:             # <<<<<<<<<<<<<<
 *                 child_index += 1
 *                 if child_index == idx:
 */
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":214
 *                 if child_index == idx:
 *                     return arc.child
 *             incr(arcs_it)             # <<<<<<<<<<<<<<
 * 
 *         return -1
 */
    (void)((++__pyx_v_arcs_it));
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":216
 *             incr(arcs_it)
 * 
 *         return -1             # <<<<<<<<<<<<<<
 * 
 *     int L(int head, int idx) nogil const:
 */
  __pyx_r = -1;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":194
 *             return this._ents.back().start
 * 
 *     int nth_child(const unordered_map[int, vector[ArcC]]& heads_arcs, int head, int idx) nogil const:             # <<<<<<<<<<<<<<
 *         if idx < 1:
 *             return -1
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":218
 *         return -1
 * 
 *     int L(int head, int idx) nogil const:             # <<<<<<<<<<<<<<
 *         return this.nth_child(this._left_arcs, head, idx)
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::L(int __pyx_v_head, int __pyx_v_idx) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":219
 * 
 *     int L(int head, int idx) nogil const:
 *         return this.nth_child(this._left_arcs, head, idx)             # <<<<<<<<<<<<<<
 * 
 *     int R(int head, int idx) nogil const:
 */
  __pyx_r = this->nth_child(this->_left_arcs, __pyx_v_head, __pyx_v_idx);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":218
 *         return -1
 * 
 *     int L(int head, int idx) nogil const:             # <<<<<<<<<<<<<<
 *         return this.nth_child(this._left_arcs, head, idx)
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":221
 *         return this.nth_child(this._left_arcs, head, idx)
 * 
 *     int R(int head, int idx) nogil const:             # <<<<<<<<<<<<<<
 *         return this.nth_child(this._right_arcs, head, idx)
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::R(int __pyx_v_head, int __pyx_v_idx) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":222
 * 
 *     int R(int head, int idx) nogil const:
 *         return this.nth_child(this._right_arcs, head, idx)             # <<<<<<<<<<<<<<
 * 
 *     bint empty() nogil const:
 */
  __pyx_r = this->nth_child(this->_right_arcs, __pyx_v_head, __pyx_v_idx);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":221
 *         return this.nth_child(this._left_arcs, head, idx)
 * 
 *     int R(int head, int idx) nogil const:             # <<<<<<<<<<<<<<
 *         return this.nth_child(this._right_arcs, head, idx)
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":224
 *         return this.nth_child(this._right_arcs, head, idx)
 * 
 *     bint empty() nogil const:             # <<<<<<<<<<<<<<
 *         return this._stack.size() == 0
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::empty(void) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":225
 * 
 *     bint empty() nogil const:
 *         return this._stack.size() == 0             # <<<<<<<<<<<<<<
 * 
 *     bint eol() nogil const:
 */
  __pyx_r = (this->_stack.size() == 0);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":224
 *         return this.nth_child(this._right_arcs, head, idx)
 * 
 *     bint empty() nogil const:             # <<<<<<<<<<<<<<
 *         return this._stack.size() == 0
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":227
 *         return this._stack.size() == 0
 * 
 *     bint eol() nogil const:             # <<<<<<<<<<<<<<
 *         return this.buffer_length() == 0
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::eol(void) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":228
 * 
 *     bint eol() nogil const:
 *         return this.buffer_length() == 0             # <<<<<<<<<<<<<<
 * 
 *     bint is_final() nogil const:
 */
  __pyx_r = (this->buffer_length() == 0);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":227
 *         return this._stack.size() == 0
 * 
 *     bint eol() nogil const:             # <<<<<<<<<<<<<<
 *         return this.buffer_length() == 0
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":230
 *         return this.buffer_length() == 0
 * 
 *     bint is_final() nogil const:             # <<<<<<<<<<<<<<
 *         return this.stack_depth() <= 0 and this.eol()
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::is_final(void) const {
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":231
 * 
 *     bint is_final() nogil const:
 *         return this.stack_depth() <= 0 and this.eol()             # <<<<<<<<<<<<<<
 * 
 *     int cannot_sent_start(int word) nogil const:
 */
  __pyx_t_2 = ((this->stack_depth() <= 0) != 0);
  if (__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L3_bool_binop_done;
  }
  __pyx_t_2 = (this->eol() != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L3_bool_binop_done:;
  __pyx_r = __pyx_t_1;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":230
 *         return this.buffer_length() == 0
 * 
 *     bint is_final() nogil const:             # <<<<<<<<<<<<<<
 *         return this.stack_depth() <= 0 and this.eol()
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":233
 *         return this.stack_depth() <= 0 and this.eol()
 * 
 *     int cannot_sent_start(int word) nogil const:             # <<<<<<<<<<<<<<
 *         if word < 0 or word >= this.length:
 *             return 0
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::cannot_sent_start(int __pyx_v_word) const {
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":234
 * 
 *     int cannot_sent_start(int word) nogil const:
 *         if word < 0 or word >= this.length:             # <<<<<<<<<<<<<<
 *             return 0
 *         elif this._sent[word].sent_start == -1:
 */
  __pyx_t_2 = ((__pyx_v_word < 0) != 0);
  if (!__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_2 = ((__pyx_v_word >= this->length) != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":235
 *     int cannot_sent_start(int word) nogil const:
 *         if word < 0 or word >= this.length:
 *             return 0             # <<<<<<<<<<<<<<
 *         elif this._sent[word].sent_start == -1:
 *             return 1
 */
    __pyx_r = 0;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":234
 * 
 *     int cannot_sent_start(int word) nogil const:
 *         if word < 0 or word >= this.length:             # <<<<<<<<<<<<<<
 *             return 0
 *         elif this._sent[word].sent_start == -1:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":236
 *         if word < 0 or word >= this.length:
 *             return 0
 *         elif this._sent[word].sent_start == -1:             # <<<<<<<<<<<<<<
 *             return 1
 *         else:
 */
  __pyx_t_1 = (((this->_sent[__pyx_v_word]).sent_start == -1L) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":237
 *             return 0
 *         elif this._sent[word].sent_start == -1:
 *             return 1             # <<<<<<<<<<<<<<
 *         else:
 *             return 0
 */
    __pyx_r = 1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":236
 *         if word < 0 or word >= this.length:
 *             return 0
 *         elif this._sent[word].sent_start == -1:             # <<<<<<<<<<<<<<
 *             return 1
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":239
 *             return 1
 *         else:
 *             return 0             # <<<<<<<<<<<<<<
 * 
 *     int is_sent_start(int word) nogil const:
 */
  /*else*/ {
    __pyx_r = 0;
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":233
 *         return this.stack_depth() <= 0 and this.eol()
 * 
 *     int cannot_sent_start(int word) nogil const:             # <<<<<<<<<<<<<<
 *         if word < 0 or word >= this.length:
 *             return 0
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":241
 *             return 0
 * 
 *     int is_sent_start(int word) nogil const:             # <<<<<<<<<<<<<<
 *         if word < 0 or word >= this.length:
 *             return 0
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::is_sent_start(int __pyx_v_word) const {
  int __pyx_r;
  int __pyx_t_1;
  int __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":242
 * 
 *     int is_sent_start(int word) nogil const:
 *         if word < 0 or word >= this.length:             # <<<<<<<<<<<<<<
 *             return 0
 *         elif this._sent[word].sent_start == 1:
 */
  __pyx_t_2 = ((__pyx_v_word < 0) != 0);
  if (!__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L4_bool_binop_done;
  }
  __pyx_t_2 = ((__pyx_v_word >= this->length) != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L4_bool_binop_done:;
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":243
 *     int is_sent_start(int word) nogil const:
 *         if word < 0 or word >= this.length:
 *             return 0             # <<<<<<<<<<<<<<
 *         elif this._sent[word].sent_start == 1:
 *             return 1
 */
    __pyx_r = 0;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":242
 * 
 *     int is_sent_start(int word) nogil const:
 *         if word < 0 or word >= this.length:             # <<<<<<<<<<<<<<
 *             return 0
 *         elif this._sent[word].sent_start == 1:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":244
 *         if word < 0 or word >= this.length:
 *             return 0
 *         elif this._sent[word].sent_start == 1:             # <<<<<<<<<<<<<<
 *             return 1
 *         elif this._sent_starts.count(word) >= 1:
 */
  __pyx_t_1 = (((this->_sent[__pyx_v_word]).sent_start == 1) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":245
 *             return 0
 *         elif this._sent[word].sent_start == 1:
 *             return 1             # <<<<<<<<<<<<<<
 *         elif this._sent_starts.count(word) >= 1:
 *             return 1
 */
    __pyx_r = 1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":244
 *         if word < 0 or word >= this.length:
 *             return 0
 *         elif this._sent[word].sent_start == 1:             # <<<<<<<<<<<<<<
 *             return 1
 *         elif this._sent_starts.count(word) >= 1:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":246
 *         elif this._sent[word].sent_start == 1:
 *             return 1
 *         elif this._sent_starts.count(word) >= 1:             # <<<<<<<<<<<<<<
 *             return 1
 *         else:
 */
  __pyx_t_1 = ((this->_sent_starts.count(__pyx_v_word) >= 1) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":247
 *             return 1
 *         elif this._sent_starts.count(word) >= 1:
 *             return 1             # <<<<<<<<<<<<<<
 *         else:
 *             return 0
 */
    __pyx_r = 1;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":246
 *         elif this._sent[word].sent_start == 1:
 *             return 1
 *         elif this._sent_starts.count(word) >= 1:             # <<<<<<<<<<<<<<
 *             return 1
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":249
 *             return 1
 *         else:
 *             return 0             # <<<<<<<<<<<<<<
 * 
 *     void set_sent_start(int word, int value) nogil:
 */
  /*else*/ {
    __pyx_r = 0;
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":241
 *             return 0
 * 
 *     int is_sent_start(int word) nogil const:             # <<<<<<<<<<<<<<
 *         if word < 0 or word >= this.length:
 *             return 0
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":251
 *             return 0
 * 
 *     void set_sent_start(int word, int value) nogil:             # <<<<<<<<<<<<<<
 *         if value >= 1:
 *             this._sent_starts.insert(word)
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::set_sent_start(int __pyx_v_word, int __pyx_v_value) {
  int __pyx_t_1;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":252
 * 
 *     void set_sent_start(int word, int value) nogil:
 *         if value >= 1:             # <<<<<<<<<<<<<<
 *             this._sent_starts.insert(word)
 * 
 */
  __pyx_t_1 = ((__pyx_v_value >= 1) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":253
 *     void set_sent_start(int word, int value) nogil:
 *         if value >= 1:
 *             this._sent_starts.insert(word)             # <<<<<<<<<<<<<<
 * 
 *     bint has_head(int child) nogil const:
 */
    try {
      this->_sent_starts.insert(__pyx_v_word);
    } catch(...) {
      #ifdef WITH_THREAD
      PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      __Pyx_CppExn2PyErr();
      #ifdef WITH_THREAD
      __Pyx_PyGILState_Release(__pyx_gilstate_save);
      #endif
      __PYX_ERR(0, 253, __pyx_L1_error)
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":252
 * 
 *     void set_sent_start(int word, int value) nogil:
 *         if value >= 1:             # <<<<<<<<<<<<<<
 *             this._sent_starts.insert(word)
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":251
 *             return 0
 * 
 *     void set_sent_start(int word, int value) nogil:             # <<<<<<<<<<<<<<
 *         if value >= 1:
 *             this._sent_starts.insert(word)
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.set_sent_start", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":255
 *             this._sent_starts.insert(word)
 * 
 *     bint has_head(int child) nogil const:             # <<<<<<<<<<<<<<
 *         return this._heads[child] >= 0
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::has_head(int __pyx_v_child) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":256
 * 
 *     bint has_head(int child) nogil const:
 *         return this._heads[child] >= 0             # <<<<<<<<<<<<<<
 * 
 *     int l_edge(int word) nogil const:
 */
  __pyx_r = ((this->_heads[__pyx_v_child]) >= 0);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":255
 *             this._sent_starts.insert(word)
 * 
 *     bint has_head(int child) nogil const:             # <<<<<<<<<<<<<<
 *         return this._heads[child] >= 0
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":258
 *         return this._heads[child] >= 0
 * 
 *     int l_edge(int word) nogil const:             # <<<<<<<<<<<<<<
 *         return word
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::l_edge(int __pyx_v_word) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":259
 * 
 *     int l_edge(int word) nogil const:
 *         return word             # <<<<<<<<<<<<<<
 * 
 *     int r_edge(int word) nogil const:
 */
  __pyx_r = __pyx_v_word;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":258
 *         return this._heads[child] >= 0
 * 
 *     int l_edge(int word) nogil const:             # <<<<<<<<<<<<<<
 *         return word
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":261
 *         return word
 * 
 *     int r_edge(int word) nogil const:             # <<<<<<<<<<<<<<
 *         return word
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::r_edge(int __pyx_v_word) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":262
 * 
 *     int r_edge(int word) nogil const:
 *         return word             # <<<<<<<<<<<<<<
 * 
 *     int n_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, int head) nogil const:
 */
  __pyx_r = __pyx_v_word;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":261
 *         return word
 * 
 *     int r_edge(int word) nogil const:             # <<<<<<<<<<<<<<
 *         return word
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":264
 *         return word
 * 
 *     int n_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, int head) nogil const:             # <<<<<<<<<<<<<<
 *         cdef int n = 0
 *         head_arcs_it = heads_arcs.const_find(head)
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::n_arcs(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  const &__pyx_v_heads_arcs, int __pyx_v_head) const {
  int __pyx_v_n;
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> > ::const_iterator __pyx_v_head_arcs_it;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  const *__pyx_v_arcs;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::const_iterator __pyx_v_arcs_it;
  struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC __pyx_v_arc;
  int __pyx_r;
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":265
 * 
 *     int n_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, int head) nogil const:
 *         cdef int n = 0             # <<<<<<<<<<<<<<
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():
 */
  __pyx_v_n = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":266
 *     int n_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, int head) nogil const:
 *         cdef int n = 0
 *         head_arcs_it = heads_arcs.const_find(head)             # <<<<<<<<<<<<<<
 *         if head_arcs_it == heads_arcs.const_end():
 *             return n
 */
  __pyx_v_head_arcs_it = __pyx_v_heads_arcs.find(__pyx_v_head);

  /* "spacy/pipeline/_parser_internals/_state.pxd":267
 *         cdef int n = 0
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():             # <<<<<<<<<<<<<<
 *             return n
 * 
 */
  __pyx_t_1 = ((__pyx_v_head_arcs_it == __pyx_v_heads_arcs.end()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":268
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():
 *             return n             # <<<<<<<<<<<<<<
 * 
 *         cdef const vector[ArcC]* arcs = &deref(head_arcs_it).second
 */
    __pyx_r = __pyx_v_n;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":267
 *         cdef int n = 0
 *         head_arcs_it = heads_arcs.const_find(head)
 *         if head_arcs_it == heads_arcs.const_end():             # <<<<<<<<<<<<<<
 *             return n
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":270
 *             return n
 * 
 *         cdef const vector[ArcC]* arcs = &deref(head_arcs_it).second             # <<<<<<<<<<<<<<
 *         arcs_it = arcs.const_begin()
 *         while arcs_it != arcs.end():
 */
  __pyx_v_arcs = (&(*__pyx_v_head_arcs_it).second);

  /* "spacy/pipeline/_parser_internals/_state.pxd":271
 * 
 *         cdef const vector[ArcC]* arcs = &deref(head_arcs_it).second
 *         arcs_it = arcs.const_begin()             # <<<<<<<<<<<<<<
 *         while arcs_it != arcs.end():
 *             arc = deref(arcs_it)
 */
  __pyx_v_arcs_it = __pyx_v_arcs->begin();

  /* "spacy/pipeline/_parser_internals/_state.pxd":272
 *         cdef const vector[ArcC]* arcs = &deref(head_arcs_it).second
 *         arcs_it = arcs.const_begin()
 *         while arcs_it != arcs.end():             # <<<<<<<<<<<<<<
 *             arc = deref(arcs_it)
 *             if arc.child != -1:
 */
  while (1) {
    __pyx_t_1 = ((__pyx_v_arcs_it != __pyx_v_arcs->end()) != 0);
    if (!__pyx_t_1) break;

    /* "spacy/pipeline/_parser_internals/_state.pxd":273
 *         arcs_it = arcs.const_begin()
 *         while arcs_it != arcs.end():
 *             arc = deref(arcs_it)             # <<<<<<<<<<<<<<
 *             if arc.child != -1:
 *                 n += 1
 */
    __pyx_v_arc = (*__pyx_v_arcs_it);

    /* "spacy/pipeline/_parser_internals/_state.pxd":274
 *         while arcs_it != arcs.end():
 *             arc = deref(arcs_it)
 *             if arc.child != -1:             # <<<<<<<<<<<<<<
 *                 n += 1
 *             incr(arcs_it)
 */
    __pyx_t_1 = ((__pyx_v_arc.child != -1L) != 0);
    if (__pyx_t_1) {

      /* "spacy/pipeline/_parser_internals/_state.pxd":275
 *             arc = deref(arcs_it)
 *             if arc.child != -1:
 *                 n += 1             # <<<<<<<<<<<<<<
 *             incr(arcs_it)
 * 
 */
      __pyx_v_n = (__pyx_v_n + 1);

      /* "spacy/pipeline/_parser_internals/_state.pxd":274
 *         while arcs_it != arcs.end():
 *             arc = deref(arcs_it)
 *             if arc.child != -1:             # <<<<<<<<<<<<<<
 *                 n += 1
 *             incr(arcs_it)
 */
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":276
 *             if arc.child != -1:
 *                 n += 1
 *             incr(arcs_it)             # <<<<<<<<<<<<<<
 * 
 *         return n
 */
    (void)((++__pyx_v_arcs_it));
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":278
 *             incr(arcs_it)
 * 
 *         return n             # <<<<<<<<<<<<<<
 * 
 *     int n_L(int head) nogil const:
 */
  __pyx_r = __pyx_v_n;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":264
 *         return word
 * 
 *     int n_arcs(const unordered_map[int, vector[ArcC]] &heads_arcs, int head) nogil const:             # <<<<<<<<<<<<<<
 *         cdef int n = 0
 *         head_arcs_it = heads_arcs.const_find(head)
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":280
 *         return n
 * 
 *     int n_L(int head) nogil const:             # <<<<<<<<<<<<<<
 *         return n_arcs(this._left_arcs, head)
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::n_L(int __pyx_v_head) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":281
 * 
 *     int n_L(int head) nogil const:
 *         return n_arcs(this._left_arcs, head)             # <<<<<<<<<<<<<<
 * 
 *     int n_R(int head) nogil const:
 */
  __pyx_r = n_arcs(this->_left_arcs, __pyx_v_head);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":280
 *         return n
 * 
 *     int n_L(int head) nogil const:             # <<<<<<<<<<<<<<
 *         return n_arcs(this._left_arcs, head)
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":283
 *         return n_arcs(this._left_arcs, head)
 * 
 *     int n_R(int head) nogil const:             # <<<<<<<<<<<<<<
 *         return n_arcs(this._right_arcs, head)
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::n_R(int __pyx_v_head) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":284
 * 
 *     int n_R(int head) nogil const:
 *         return n_arcs(this._right_arcs, head)             # <<<<<<<<<<<<<<
 * 
 *     bint stack_is_connected() nogil const:
 */
  __pyx_r = n_arcs(this->_right_arcs, __pyx_v_head);
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":283
 *         return n_arcs(this._left_arcs, head)
 * 
 *     int n_R(int head) nogil const:             # <<<<<<<<<<<<<<
 *         return n_arcs(this._right_arcs, head)
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":286
 *         return n_arcs(this._right_arcs, head)
 * 
 *     bint stack_is_connected() nogil const:             # <<<<<<<<<<<<<<
 *         return False
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::stack_is_connected(void) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":287
 * 
 *     bint stack_is_connected() nogil const:
 *         return False             # <<<<<<<<<<<<<<
 * 
 *     bint entity_is_open() nogil const:
 */
  __pyx_r = 0;
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":286
 *         return n_arcs(this._right_arcs, head)
 * 
 *     bint stack_is_connected() nogil const:             # <<<<<<<<<<<<<<
 *         return False
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":289
 *         return False
 * 
 *     bint entity_is_open() nogil const:             # <<<<<<<<<<<<<<
 *         if this._ents.size() == 0:
 *             return False
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::entity_is_open(void) const {
  int __pyx_r;
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":290
 * 
 *     bint entity_is_open() nogil const:
 *         if this._ents.size() == 0:             # <<<<<<<<<<<<<<
 *             return False
 *         else:
 */
  __pyx_t_1 = ((this->_ents.size() == 0) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":291
 *     bint entity_is_open() nogil const:
 *         if this._ents.size() == 0:
 *             return False             # <<<<<<<<<<<<<<
 *         else:
 *             return this._ents.back().end == -1
 */
    __pyx_r = 0;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":290
 * 
 *     bint entity_is_open() nogil const:
 *         if this._ents.size() == 0:             # <<<<<<<<<<<<<<
 *             return False
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":293
 *             return False
 *         else:
 *             return this._ents.back().end == -1             # <<<<<<<<<<<<<<
 * 
 *     int stack_depth() nogil const:
 */
  /*else*/ {
    __pyx_r = (this->_ents.back().end == -1L);
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":289
 *         return False
 * 
 *     bint entity_is_open() nogil const:             # <<<<<<<<<<<<<<
 *         if this._ents.size() == 0:
 *             return False
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":295
 *             return this._ents.back().end == -1
 * 
 *     int stack_depth() nogil const:             # <<<<<<<<<<<<<<
 *         return this._stack.size()
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::stack_depth(void) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":296
 * 
 *     int stack_depth() nogil const:
 *         return this._stack.size()             # <<<<<<<<<<<<<<
 * 
 *     int buffer_length() nogil const:
 */
  __pyx_r = this->_stack.size();
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":295
 *             return this._ents.back().end == -1
 * 
 *     int stack_depth() nogil const:             # <<<<<<<<<<<<<<
 *         return this._stack.size()
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":298
 *         return this._stack.size()
 * 
 *     int buffer_length() nogil const:             # <<<<<<<<<<<<<<
 *         return (this.length - this._b_i) + this._rebuffer.size()
 * 
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::buffer_length(void) const {
  int __pyx_r;

  /* "spacy/pipeline/_parser_internals/_state.pxd":299
 * 
 *     int buffer_length() nogil const:
 *         return (this.length - this._b_i) + this._rebuffer.size()             # <<<<<<<<<<<<<<
 * 
 *     void push() nogil:
 */
  __pyx_r = ((this->length - this->_b_i) + this->_rebuffer.size());
  goto __pyx_L0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":298
 *         return this._stack.size()
 * 
 *     int buffer_length() nogil const:             # <<<<<<<<<<<<<<
 *         return (this.length - this._b_i) + this._rebuffer.size()
 * 
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":301
 *         return (this.length - this._b_i) + this._rebuffer.size()
 * 
 *     void push() nogil:             # <<<<<<<<<<<<<<
 *         b0 = this.B(0)
 *         if this._rebuffer.size():
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::push(void) {
  int __pyx_v_b0;
  int __pyx_t_1;
  int __pyx_t_2;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":302
 * 
 *     void push() nogil:
 *         b0 = this.B(0)             # <<<<<<<<<<<<<<
 *         if this._rebuffer.size():
 *             b0 = this._rebuffer.back()
 */
  __pyx_v_b0 = this->B(0);

  /* "spacy/pipeline/_parser_internals/_state.pxd":303
 *     void push() nogil:
 *         b0 = this.B(0)
 *         if this._rebuffer.size():             # <<<<<<<<<<<<<<
 *             b0 = this._rebuffer.back()
 *             this._rebuffer.pop_back()
 */
  __pyx_t_1 = (this->_rebuffer.size() != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":304
 *         b0 = this.B(0)
 *         if this._rebuffer.size():
 *             b0 = this._rebuffer.back()             # <<<<<<<<<<<<<<
 *             this._rebuffer.pop_back()
 *         else:
 */
    __pyx_v_b0 = this->_rebuffer.back();

    /* "spacy/pipeline/_parser_internals/_state.pxd":305
 *         if this._rebuffer.size():
 *             b0 = this._rebuffer.back()
 *             this._rebuffer.pop_back()             # <<<<<<<<<<<<<<
 *         else:
 *             b0 = this._b_i
 */
    this->_rebuffer.pop_back();

    /* "spacy/pipeline/_parser_internals/_state.pxd":303
 *     void push() nogil:
 *         b0 = this.B(0)
 *         if this._rebuffer.size():             # <<<<<<<<<<<<<<
 *             b0 = this._rebuffer.back()
 *             this._rebuffer.pop_back()
 */
    goto __pyx_L3;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":307
 *             this._rebuffer.pop_back()
 *         else:
 *             b0 = this._b_i             # <<<<<<<<<<<<<<
 *             this._b_i += 1
 *         this._stack.push_back(b0)
 */
  /*else*/ {
    __pyx_t_2 = this->_b_i;
    __pyx_v_b0 = __pyx_t_2;

    /* "spacy/pipeline/_parser_internals/_state.pxd":308
 *         else:
 *             b0 = this._b_i
 *             this._b_i += 1             # <<<<<<<<<<<<<<
 *         this._stack.push_back(b0)
 * 
 */
    this->_b_i = (this->_b_i + 1);
  }
  __pyx_L3:;

  /* "spacy/pipeline/_parser_internals/_state.pxd":309
 *             b0 = this._b_i
 *             this._b_i += 1
 *         this._stack.push_back(b0)             # <<<<<<<<<<<<<<
 * 
 *     void pop() nogil:
 */
  try {
    this->_stack.push_back(__pyx_v_b0);
  } catch(...) {
    #ifdef WITH_THREAD
    PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
    #endif
    __Pyx_CppExn2PyErr();
    #ifdef WITH_THREAD
    __Pyx_PyGILState_Release(__pyx_gilstate_save);
    #endif
    __PYX_ERR(0, 309, __pyx_L1_error)
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":301
 *         return (this.length - this._b_i) + this._rebuffer.size()
 * 
 *     void push() nogil:             # <<<<<<<<<<<<<<
 *         b0 = this.B(0)
 *         if this._rebuffer.size():
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.push", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":311
 *         this._stack.push_back(b0)
 * 
 *     void pop() nogil:             # <<<<<<<<<<<<<<
 *         this._stack.pop_back()
 * 
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::pop(void) {

  /* "spacy/pipeline/_parser_internals/_state.pxd":312
 * 
 *     void pop() nogil:
 *         this._stack.pop_back()             # <<<<<<<<<<<<<<
 * 
 *     void force_final() nogil:
 */
  this->_stack.pop_back();

  /* "spacy/pipeline/_parser_internals/_state.pxd":311
 *         this._stack.push_back(b0)
 * 
 *     void pop() nogil:             # <<<<<<<<<<<<<<
 *         this._stack.pop_back()
 * 
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":314
 *         this._stack.pop_back()
 * 
 *     void force_final() nogil:             # <<<<<<<<<<<<<<
 *         # This should only be used in desperate situations, as it may leave
 *         # the analysis in an unexpected state.
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::force_final(void) {
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":317
 *         # This should only be used in desperate situations, as it may leave
 *         # the analysis in an unexpected state.
 *         this._stack.clear()             # <<<<<<<<<<<<<<
 *         this._b_i = this.length
 * 
 */
  this->_stack.clear();

  /* "spacy/pipeline/_parser_internals/_state.pxd":318
 *         # the analysis in an unexpected state.
 *         this._stack.clear()
 *         this._b_i = this.length             # <<<<<<<<<<<<<<
 * 
 *     void unshift() nogil:
 */
  __pyx_t_1 = this->length;
  this->_b_i = __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":314
 *         this._stack.pop_back()
 * 
 *     void force_final() nogil:             # <<<<<<<<<<<<<<
 *         # This should only be used in desperate situations, as it may leave
 *         # the analysis in an unexpected state.
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":320
 *         this._b_i = this.length
 * 
 *     void unshift() nogil:             # <<<<<<<<<<<<<<
 *         s0 = this._stack.back()
 *         this._unshiftable[s0] = 1
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::unshift(void) {
  int __pyx_v_s0;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":321
 * 
 *     void unshift() nogil:
 *         s0 = this._stack.back()             # <<<<<<<<<<<<<<
 *         this._unshiftable[s0] = 1
 *         this._rebuffer.push_back(s0)
 */
  __pyx_v_s0 = this->_stack.back();

  /* "spacy/pipeline/_parser_internals/_state.pxd":322
 *     void unshift() nogil:
 *         s0 = this._stack.back()
 *         this._unshiftable[s0] = 1             # <<<<<<<<<<<<<<
 *         this._rebuffer.push_back(s0)
 *         this._stack.pop_back()
 */
  (this->_unshiftable[__pyx_v_s0]) = 1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":323
 *         s0 = this._stack.back()
 *         this._unshiftable[s0] = 1
 *         this._rebuffer.push_back(s0)             # <<<<<<<<<<<<<<
 *         this._stack.pop_back()
 * 
 */
  try {
    this->_rebuffer.push_back(__pyx_v_s0);
  } catch(...) {
    #ifdef WITH_THREAD
    PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
    #endif
    __Pyx_CppExn2PyErr();
    #ifdef WITH_THREAD
    __Pyx_PyGILState_Release(__pyx_gilstate_save);
    #endif
    __PYX_ERR(0, 323, __pyx_L1_error)
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":324
 *         this._unshiftable[s0] = 1
 *         this._rebuffer.push_back(s0)
 *         this._stack.pop_back()             # <<<<<<<<<<<<<<
 * 
 *     int is_unshiftable(int item) nogil const:
 */
  this->_stack.pop_back();

  /* "spacy/pipeline/_parser_internals/_state.pxd":320
 *         this._b_i = this.length
 * 
 *     void unshift() nogil:             # <<<<<<<<<<<<<<
 *         s0 = this._stack.back()
 *         this._unshiftable[s0] = 1
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.unshift", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":326
 *         this._stack.pop_back()
 * 
 *     int is_unshiftable(int item) nogil const:             # <<<<<<<<<<<<<<
 *         if item >= this._unshiftable.size():
 *             return 0
 */

int __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::is_unshiftable(int __pyx_v_item) const {
  int __pyx_r;
  int __pyx_t_1;
  bool __pyx_t_2;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":327
 * 
 *     int is_unshiftable(int item) nogil const:
 *         if item >= this._unshiftable.size():             # <<<<<<<<<<<<<<
 *             return 0
 *         else:
 */
  __pyx_t_1 = ((__pyx_v_item >= this->_unshiftable.size()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":328
 *     int is_unshiftable(int item) nogil const:
 *         if item >= this._unshiftable.size():
 *             return 0             # <<<<<<<<<<<<<<
 *         else:
 *             return this._unshiftable.at(item)
 */
    __pyx_r = 0;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":327
 * 
 *     int is_unshiftable(int item) nogil const:
 *         if item >= this._unshiftable.size():             # <<<<<<<<<<<<<<
 *             return 0
 *         else:
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":330
 *             return 0
 *         else:
 *             return this._unshiftable.at(item)             # <<<<<<<<<<<<<<
 * 
 *     void set_reshiftable(int item) nogil:
 */
  /*else*/ {
    try {
      __pyx_t_2 = this->_unshiftable.at(__pyx_v_item);
    } catch(...) {
      #ifdef WITH_THREAD
      PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      __Pyx_CppExn2PyErr();
      #ifdef WITH_THREAD
      __Pyx_PyGILState_Release(__pyx_gilstate_save);
      #endif
      __PYX_ERR(0, 330, __pyx_L1_error)
    }
    __pyx_r = __pyx_t_2;
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":326
 *         this._stack.pop_back()
 * 
 *     int is_unshiftable(int item) nogil const:             # <<<<<<<<<<<<<<
 *         if item >= this._unshiftable.size():
 *             return 0
 */

  /* function exit code */
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.is_unshiftable", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_r = 0;
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":332
 *             return this._unshiftable.at(item)
 * 
 *     void set_reshiftable(int item) nogil:             # <<<<<<<<<<<<<<
 *         if item < this._unshiftable.size():
 *             this._unshiftable[item] = 0
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::set_reshiftable(int __pyx_v_item) {
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":333
 * 
 *     void set_reshiftable(int item) nogil:
 *         if item < this._unshiftable.size():             # <<<<<<<<<<<<<<
 *             this._unshiftable[item] = 0
 * 
 */
  __pyx_t_1 = ((__pyx_v_item < this->_unshiftable.size()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":334
 *     void set_reshiftable(int item) nogil:
 *         if item < this._unshiftable.size():
 *             this._unshiftable[item] = 0             # <<<<<<<<<<<<<<
 * 
 *     void add_arc(int head, int child, attr_t label) nogil:
 */
    (this->_unshiftable[__pyx_v_item]) = 0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":333
 * 
 *     void set_reshiftable(int item) nogil:
 *         if item < this._unshiftable.size():             # <<<<<<<<<<<<<<
 *             this._unshiftable[item] = 0
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":332
 *             return this._unshiftable.at(item)
 * 
 *     void set_reshiftable(int item) nogil:             # <<<<<<<<<<<<<<
 *         if item < this._unshiftable.size():
 *             this._unshiftable[item] = 0
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":336
 *             this._unshiftable[item] = 0
 * 
 *     void add_arc(int head, int child, attr_t label) nogil:             # <<<<<<<<<<<<<<
 *         if this.has_head(child):
 *             this.del_arc(this.H(child), child)
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::add_arc(int __pyx_v_head, int __pyx_v_child, __pyx_t_5spacy_8typedefs_attr_t __pyx_v_label) {
  struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC __pyx_v_arc;
  int __pyx_t_1;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":337
 * 
 *     void add_arc(int head, int child, attr_t label) nogil:
 *         if this.has_head(child):             # <<<<<<<<<<<<<<
 *             this.del_arc(this.H(child), child)
 *         cdef ArcC arc
 */
  __pyx_t_1 = (this->has_head(__pyx_v_child) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":338
 *     void add_arc(int head, int child, attr_t label) nogil:
 *         if this.has_head(child):
 *             this.del_arc(this.H(child), child)             # <<<<<<<<<<<<<<
 *         cdef ArcC arc
 *         arc.head = head
 */
    this->del_arc(this->H(__pyx_v_child), __pyx_v_child);

    /* "spacy/pipeline/_parser_internals/_state.pxd":337
 * 
 *     void add_arc(int head, int child, attr_t label) nogil:
 *         if this.has_head(child):             # <<<<<<<<<<<<<<
 *             this.del_arc(this.H(child), child)
 *         cdef ArcC arc
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":340
 *             this.del_arc(this.H(child), child)
 *         cdef ArcC arc
 *         arc.head = head             # <<<<<<<<<<<<<<
 *         arc.child = child
 *         arc.label = label
 */
  __pyx_v_arc.head = __pyx_v_head;

  /* "spacy/pipeline/_parser_internals/_state.pxd":341
 *         cdef ArcC arc
 *         arc.head = head
 *         arc.child = child             # <<<<<<<<<<<<<<
 *         arc.label = label
 *         if head > child:
 */
  __pyx_v_arc.child = __pyx_v_child;

  /* "spacy/pipeline/_parser_internals/_state.pxd":342
 *         arc.head = head
 *         arc.child = child
 *         arc.label = label             # <<<<<<<<<<<<<<
 *         if head > child:
 *             this._left_arcs[arc.head].push_back(arc)
 */
  __pyx_v_arc.label = __pyx_v_label;

  /* "spacy/pipeline/_parser_internals/_state.pxd":343
 *         arc.child = child
 *         arc.label = label
 *         if head > child:             # <<<<<<<<<<<<<<
 *             this._left_arcs[arc.head].push_back(arc)
 *         else:
 */
  __pyx_t_1 = ((__pyx_v_head > __pyx_v_child) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":344
 *         arc.label = label
 *         if head > child:
 *             this._left_arcs[arc.head].push_back(arc)             # <<<<<<<<<<<<<<
 *         else:
 *             this._right_arcs[arc.head].push_back(arc)
 */
    try {
      (this->_left_arcs[__pyx_v_arc.head]).push_back(__pyx_v_arc);
    } catch(...) {
      #ifdef WITH_THREAD
      PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      __Pyx_CppExn2PyErr();
      #ifdef WITH_THREAD
      __Pyx_PyGILState_Release(__pyx_gilstate_save);
      #endif
      __PYX_ERR(0, 344, __pyx_L1_error)
    }

    /* "spacy/pipeline/_parser_internals/_state.pxd":343
 *         arc.child = child
 *         arc.label = label
 *         if head > child:             # <<<<<<<<<<<<<<
 *             this._left_arcs[arc.head].push_back(arc)
 *         else:
 */
    goto __pyx_L4;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":346
 *             this._left_arcs[arc.head].push_back(arc)
 *         else:
 *             this._right_arcs[arc.head].push_back(arc)             # <<<<<<<<<<<<<<
 *         this._heads[child] = head
 * 
 */
  /*else*/ {
    try {
      (this->_right_arcs[__pyx_v_arc.head]).push_back(__pyx_v_arc);
    } catch(...) {
      #ifdef WITH_THREAD
      PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
      #endif
      __Pyx_CppExn2PyErr();
      #ifdef WITH_THREAD
      __Pyx_PyGILState_Release(__pyx_gilstate_save);
      #endif
      __PYX_ERR(0, 346, __pyx_L1_error)
    }
  }
  __pyx_L4:;

  /* "spacy/pipeline/_parser_internals/_state.pxd":347
 *         else:
 *             this._right_arcs[arc.head].push_back(arc)
 *         this._heads[child] = head             # <<<<<<<<<<<<<<
 * 
 *     void map_del_arc(unordered_map[int, vector[ArcC]]* heads_arcs, int h_i, int c_i) nogil:
 */
  (this->_heads[__pyx_v_child]) = __pyx_v_head;

  /* "spacy/pipeline/_parser_internals/_state.pxd":336
 *             this._unshiftable[item] = 0
 * 
 *     void add_arc(int head, int child, attr_t label) nogil:             # <<<<<<<<<<<<<<
 *         if this.has_head(child):
 *             this.del_arc(this.H(child), child)
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.add_arc", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":349
 *         this._heads[child] = head
 * 
 *     void map_del_arc(unordered_map[int, vector[ArcC]]* heads_arcs, int h_i, int c_i) nogil:             # <<<<<<<<<<<<<<
 *         arcs_it = heads_arcs.find(h_i)
 *         if arcs_it == heads_arcs.end():
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::map_del_arc(std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  *__pyx_v_heads_arcs, int __pyx_v_h_i, int __pyx_v_c_i) {
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> > ::iterator __pyx_v_arcs_it;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC>  *__pyx_v_arcs;
  struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC __pyx_v_arc;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::size_type __pyx_v_i;
  int __pyx_t_1;
  int __pyx_t_2;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::size_type __pyx_t_3;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::size_type __pyx_t_4;
  std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> ::size_type __pyx_t_5;
  __Pyx_FakeReference<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> __pyx_t_6;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":350
 * 
 *     void map_del_arc(unordered_map[int, vector[ArcC]]* heads_arcs, int h_i, int c_i) nogil:
 *         arcs_it = heads_arcs.find(h_i)             # <<<<<<<<<<<<<<
 *         if arcs_it == heads_arcs.end():
 *             return
 */
  __pyx_v_arcs_it = __pyx_v_heads_arcs->find(__pyx_v_h_i);

  /* "spacy/pipeline/_parser_internals/_state.pxd":351
 *     void map_del_arc(unordered_map[int, vector[ArcC]]* heads_arcs, int h_i, int c_i) nogil:
 *         arcs_it = heads_arcs.find(h_i)
 *         if arcs_it == heads_arcs.end():             # <<<<<<<<<<<<<<
 *             return
 * 
 */
  __pyx_t_1 = ((__pyx_v_arcs_it == __pyx_v_heads_arcs->end()) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":352
 *         arcs_it = heads_arcs.find(h_i)
 *         if arcs_it == heads_arcs.end():
 *             return             # <<<<<<<<<<<<<<
 * 
 *         arcs = &deref(arcs_it).second
 */
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":351
 *     void map_del_arc(unordered_map[int, vector[ArcC]]* heads_arcs, int h_i, int c_i) nogil:
 *         arcs_it = heads_arcs.find(h_i)
 *         if arcs_it == heads_arcs.end():             # <<<<<<<<<<<<<<
 *             return
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":354
 *             return
 * 
 *         arcs = &deref(arcs_it).second             # <<<<<<<<<<<<<<
 *         if arcs.size() == 0:
 *             return
 */
  __pyx_v_arcs = (&(*__pyx_v_arcs_it).second);

  /* "spacy/pipeline/_parser_internals/_state.pxd":355
 * 
 *         arcs = &deref(arcs_it).second
 *         if arcs.size() == 0:             # <<<<<<<<<<<<<<
 *             return
 * 
 */
  __pyx_t_1 = ((__pyx_v_arcs->size() == 0) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":356
 *         arcs = &deref(arcs_it).second
 *         if arcs.size() == 0:
 *             return             # <<<<<<<<<<<<<<
 * 
 *         arc = arcs.back()
 */
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":355
 * 
 *         arcs = &deref(arcs_it).second
 *         if arcs.size() == 0:             # <<<<<<<<<<<<<<
 *             return
 * 
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":358
 *             return
 * 
 *         arc = arcs.back()             # <<<<<<<<<<<<<<
 *         if arc.head == h_i and arc.child == c_i:
 *             arcs.pop_back()
 */
  __pyx_v_arc = __pyx_v_arcs->back();

  /* "spacy/pipeline/_parser_internals/_state.pxd":359
 * 
 *         arc = arcs.back()
 *         if arc.head == h_i and arc.child == c_i:             # <<<<<<<<<<<<<<
 *             arcs.pop_back()
 *         else:
 */
  __pyx_t_2 = ((__pyx_v_arc.head == __pyx_v_h_i) != 0);
  if (__pyx_t_2) {
  } else {
    __pyx_t_1 = __pyx_t_2;
    goto __pyx_L6_bool_binop_done;
  }
  __pyx_t_2 = ((__pyx_v_arc.child == __pyx_v_c_i) != 0);
  __pyx_t_1 = __pyx_t_2;
  __pyx_L6_bool_binop_done:;
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":360
 *         arc = arcs.back()
 *         if arc.head == h_i and arc.child == c_i:
 *             arcs.pop_back()             # <<<<<<<<<<<<<<
 *         else:
 *             for i in range(arcs.size()-1):
 */
    __pyx_v_arcs->pop_back();

    /* "spacy/pipeline/_parser_internals/_state.pxd":359
 * 
 *         arc = arcs.back()
 *         if arc.head == h_i and arc.child == c_i:             # <<<<<<<<<<<<<<
 *             arcs.pop_back()
 *         else:
 */
    goto __pyx_L5;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":362
 *             arcs.pop_back()
 *         else:
 *             for i in range(arcs.size()-1):             # <<<<<<<<<<<<<<
 *                 arc = arcs.at(i)
 *                 if arc.head == h_i and arc.child == c_i:
 */
  /*else*/ {
    __pyx_t_3 = (__pyx_v_arcs->size() - 1);
    __pyx_t_4 = __pyx_t_3;
    for (__pyx_t_5 = 0; __pyx_t_5 < __pyx_t_4; __pyx_t_5+=1) {
      __pyx_v_i = __pyx_t_5;

      /* "spacy/pipeline/_parser_internals/_state.pxd":363
 *         else:
 *             for i in range(arcs.size()-1):
 *                 arc = arcs.at(i)             # <<<<<<<<<<<<<<
 *                 if arc.head == h_i and arc.child == c_i:
 *                     arc.head = -1
 */
      try {
        __pyx_t_6 = __pyx_v_arcs->at(__pyx_v_i);
      } catch(...) {
        #ifdef WITH_THREAD
        PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
        #endif
        __Pyx_CppExn2PyErr();
        #ifdef WITH_THREAD
        __Pyx_PyGILState_Release(__pyx_gilstate_save);
        #endif
        __PYX_ERR(0, 363, __pyx_L1_error)
      }
      __pyx_v_arc = __pyx_t_6;

      /* "spacy/pipeline/_parser_internals/_state.pxd":364
 *             for i in range(arcs.size()-1):
 *                 arc = arcs.at(i)
 *                 if arc.head == h_i and arc.child == c_i:             # <<<<<<<<<<<<<<
 *                     arc.head = -1
 *                     arc.child = -1
 */
      __pyx_t_2 = ((__pyx_v_arc.head == __pyx_v_h_i) != 0);
      if (__pyx_t_2) {
      } else {
        __pyx_t_1 = __pyx_t_2;
        goto __pyx_L11_bool_binop_done;
      }
      __pyx_t_2 = ((__pyx_v_arc.child == __pyx_v_c_i) != 0);
      __pyx_t_1 = __pyx_t_2;
      __pyx_L11_bool_binop_done:;
      if (__pyx_t_1) {

        /* "spacy/pipeline/_parser_internals/_state.pxd":365
 *                 arc = arcs.at(i)
 *                 if arc.head == h_i and arc.child == c_i:
 *                     arc.head = -1             # <<<<<<<<<<<<<<
 *                     arc.child = -1
 *                     arc.label = 0
 */
        __pyx_v_arc.head = -1;

        /* "spacy/pipeline/_parser_internals/_state.pxd":366
 *                 if arc.head == h_i and arc.child == c_i:
 *                     arc.head = -1
 *                     arc.child = -1             # <<<<<<<<<<<<<<
 *                     arc.label = 0
 *                     break
 */
        __pyx_v_arc.child = -1;

        /* "spacy/pipeline/_parser_internals/_state.pxd":367
 *                     arc.head = -1
 *                     arc.child = -1
 *                     arc.label = 0             # <<<<<<<<<<<<<<
 *                     break
 * 
 */
        __pyx_v_arc.label = 0;

        /* "spacy/pipeline/_parser_internals/_state.pxd":368
 *                     arc.child = -1
 *                     arc.label = 0
 *                     break             # <<<<<<<<<<<<<<
 * 
 *     void del_arc(int h_i, int c_i) nogil:
 */
        goto __pyx_L9_break;

        /* "spacy/pipeline/_parser_internals/_state.pxd":364
 *             for i in range(arcs.size()-1):
 *                 arc = arcs.at(i)
 *                 if arc.head == h_i and arc.child == c_i:             # <<<<<<<<<<<<<<
 *                     arc.head = -1
 *                     arc.child = -1
 */
      }
    }
    __pyx_L9_break:;
  }
  __pyx_L5:;

  /* "spacy/pipeline/_parser_internals/_state.pxd":349
 *         this._heads[child] = head
 * 
 *     void map_del_arc(unordered_map[int, vector[ArcC]]* heads_arcs, int h_i, int c_i) nogil:             # <<<<<<<<<<<<<<
 *         arcs_it = heads_arcs.find(h_i)
 *         if arcs_it == heads_arcs.end():
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.map_del_arc", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":370
 *                     break
 * 
 *     void del_arc(int h_i, int c_i) nogil:             # <<<<<<<<<<<<<<
 *         if h_i > c_i:
 *             this.map_del_arc(&this._left_arcs, h_i, c_i)
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::del_arc(int __pyx_v_h_i, int __pyx_v_c_i) {
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":371
 * 
 *     void del_arc(int h_i, int c_i) nogil:
 *         if h_i > c_i:             # <<<<<<<<<<<<<<
 *             this.map_del_arc(&this._left_arcs, h_i, c_i)
 *         else:
 */
  __pyx_t_1 = ((__pyx_v_h_i > __pyx_v_c_i) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":372
 *     void del_arc(int h_i, int c_i) nogil:
 *         if h_i > c_i:
 *             this.map_del_arc(&this._left_arcs, h_i, c_i)             # <<<<<<<<<<<<<<
 *         else:
 *             this.map_del_arc(&this._right_arcs, h_i, c_i)
 */
    this->map_del_arc((&this->_left_arcs), __pyx_v_h_i, __pyx_v_c_i);

    /* "spacy/pipeline/_parser_internals/_state.pxd":371
 * 
 *     void del_arc(int h_i, int c_i) nogil:
 *         if h_i > c_i:             # <<<<<<<<<<<<<<
 *             this.map_del_arc(&this._left_arcs, h_i, c_i)
 *         else:
 */
    goto __pyx_L3;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":374
 *             this.map_del_arc(&this._left_arcs, h_i, c_i)
 *         else:
 *             this.map_del_arc(&this._right_arcs, h_i, c_i)             # <<<<<<<<<<<<<<
 * 
 *     SpanC get_ent() nogil const:
 */
  /*else*/ {
    this->map_del_arc((&this->_right_arcs), __pyx_v_h_i, __pyx_v_c_i);
  }
  __pyx_L3:;

  /* "spacy/pipeline/_parser_internals/_state.pxd":370
 *                     break
 * 
 *     void del_arc(int h_i, int c_i) nogil:             # <<<<<<<<<<<<<<
 *         if h_i > c_i:
 *             this.map_del_arc(&this._left_arcs, h_i, c_i)
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":376
 *             this.map_del_arc(&this._right_arcs, h_i, c_i)
 * 
 *     SpanC get_ent() nogil const:             # <<<<<<<<<<<<<<
 *         cdef SpanC ent
 *         if this._ents.size() == 0:
 */

struct __pyx_t_5spacy_7structs_SpanC __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::get_ent(void) const {
  struct __pyx_t_5spacy_7structs_SpanC __pyx_v_ent;
  struct __pyx_t_5spacy_7structs_SpanC __pyx_r;
  int __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":378
 *     SpanC get_ent() nogil const:
 *         cdef SpanC ent
 *         if this._ents.size() == 0:             # <<<<<<<<<<<<<<
 *             ent.start = 0
 *             ent.end = 0
 */
  __pyx_t_1 = ((this->_ents.size() == 0) != 0);
  if (__pyx_t_1) {

    /* "spacy/pipeline/_parser_internals/_state.pxd":379
 *         cdef SpanC ent
 *         if this._ents.size() == 0:
 *             ent.start = 0             # <<<<<<<<<<<<<<
 *             ent.end = 0
 *             ent.label = 0
 */
    __pyx_v_ent.start = 0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":380
 *         if this._ents.size() == 0:
 *             ent.start = 0
 *             ent.end = 0             # <<<<<<<<<<<<<<
 *             ent.label = 0
 *             return ent
 */
    __pyx_v_ent.end = 0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":381
 *             ent.start = 0
 *             ent.end = 0
 *             ent.label = 0             # <<<<<<<<<<<<<<
 *             return ent
 *         else:
 */
    __pyx_v_ent.label = 0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":382
 *             ent.end = 0
 *             ent.label = 0
 *             return ent             # <<<<<<<<<<<<<<
 *         else:
 *             return this._ents.back()
 */
    __pyx_r = __pyx_v_ent;
    goto __pyx_L0;

    /* "spacy/pipeline/_parser_internals/_state.pxd":378
 *     SpanC get_ent() nogil const:
 *         cdef SpanC ent
 *         if this._ents.size() == 0:             # <<<<<<<<<<<<<<
 *             ent.start = 0
 *             ent.end = 0
 */
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":384
 *             return ent
 *         else:
 *             return this._ents.back()             # <<<<<<<<<<<<<<
 * 
 *     void open_ent(attr_t label) nogil:
 */
  /*else*/ {
    __pyx_r = this->_ents.back();
    goto __pyx_L0;
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":376
 *             this.map_del_arc(&this._right_arcs, h_i, c_i)
 * 
 *     SpanC get_ent() nogil const:             # <<<<<<<<<<<<<<
 *         cdef SpanC ent
 *         if this._ents.size() == 0:
 */

  /* function exit code */
  __pyx_L0:;
  return __pyx_r;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":386
 *             return this._ents.back()
 * 
 *     void open_ent(attr_t label) nogil:             # <<<<<<<<<<<<<<
 *         cdef SpanC ent
 *         ent.start = this.B(0)
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::open_ent(__pyx_t_5spacy_8typedefs_attr_t __pyx_v_label) {
  struct __pyx_t_5spacy_7structs_SpanC __pyx_v_ent;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":388
 *     void open_ent(attr_t label) nogil:
 *         cdef SpanC ent
 *         ent.start = this.B(0)             # <<<<<<<<<<<<<<
 *         ent.label = label
 *         ent.end = -1
 */
  __pyx_v_ent.start = this->B(0);

  /* "spacy/pipeline/_parser_internals/_state.pxd":389
 *         cdef SpanC ent
 *         ent.start = this.B(0)
 *         ent.label = label             # <<<<<<<<<<<<<<
 *         ent.end = -1
 *         this._ents.push_back(ent)
 */
  __pyx_v_ent.label = __pyx_v_label;

  /* "spacy/pipeline/_parser_internals/_state.pxd":390
 *         ent.start = this.B(0)
 *         ent.label = label
 *         ent.end = -1             # <<<<<<<<<<<<<<
 *         this._ents.push_back(ent)
 * 
 */
  __pyx_v_ent.end = -1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":391
 *         ent.label = label
 *         ent.end = -1
 *         this._ents.push_back(ent)             # <<<<<<<<<<<<<<
 * 
 *     void close_ent() nogil:
 */
  try {
    this->_ents.push_back(__pyx_v_ent);
  } catch(...) {
    #ifdef WITH_THREAD
    PyGILState_STATE __pyx_gilstate_save = __Pyx_PyGILState_Ensure();
    #endif
    __Pyx_CppExn2PyErr();
    #ifdef WITH_THREAD
    __Pyx_PyGILState_Release(__pyx_gilstate_save);
    #endif
    __PYX_ERR(0, 391, __pyx_L1_error)
  }

  /* "spacy/pipeline/_parser_internals/_state.pxd":386
 *             return this._ents.back()
 * 
 *     void open_ent(attr_t label) nogil:             # <<<<<<<<<<<<<<
 *         cdef SpanC ent
 *         ent.start = this.B(0)
 */

  /* function exit code */
  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_WriteUnraisable("StateC.open_ent", __pyx_clineno, __pyx_lineno, __pyx_filename, 1, 1);
  __pyx_L0:;
}

/* "spacy/pipeline/_parser_internals/_state.pxd":393
 *         this._ents.push_back(ent)
 * 
 *     void close_ent() nogil:             # <<<<<<<<<<<<<<
 *         this._ents.back().end = this.B(0)+1
 * 
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::close_ent(void) {

  /* "spacy/pipeline/_parser_internals/_state.pxd":394
 * 
 *     void close_ent() nogil:
 *         this._ents.back().end = this.B(0)+1             # <<<<<<<<<<<<<<
 * 
 *     void clone(const StateC* src) nogil:
 */
  this->_ents.back().end = (this->B(0) + 1);

  /* "spacy/pipeline/_parser_internals/_state.pxd":393
 *         this._ents.push_back(ent)
 * 
 *     void close_ent() nogil:             # <<<<<<<<<<<<<<
 *         this._ents.back().end = this.B(0)+1
 * 
 */

  /* function exit code */
}

/* "spacy/pipeline/_parser_internals/_state.pxd":396
 *         this._ents.back().end = this.B(0)+1
 * 
 *     void clone(const StateC* src) nogil:             # <<<<<<<<<<<<<<
 *         this.length = src.length
 *         this._sent = src._sent
 */

void __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC::clone(__pyx_t_5spacy_8pipeline_17_parser_internals_6_state_StateC const *__pyx_v_src) {
  int __pyx_t_1;
  struct __pyx_t_5spacy_7structs_TokenC const *__pyx_t_2;
  std::vector<int>  __pyx_t_3;
  std::set<int>  __pyx_t_4;
  std::vector<bool>  __pyx_t_5;
  std::vector<struct __pyx_t_5spacy_7structs_SpanC>  __pyx_t_6;
  std::unordered_map<int,std::vector<struct __pyx_t_5spacy_8pipeline_17_parser_internals_6_state_ArcC> >  __pyx_t_7;
  struct __pyx_t_5spacy_7structs_TokenC __pyx_t_8;

  /* "spacy/pipeline/_parser_internals/_state.pxd":397
 * 
 *     void clone(const StateC* src) nogil:
 *         this.length = src.length             # <<<<<<<<<<<<<<
 *         this._sent = src._sent
 *         this._stack = src._stack
 */
  __pyx_t_1 = __pyx_v_src->length;
  this->length = __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":398
 *     void clone(const StateC* src) nogil:
 *         this.length = src.length
 *         this._sent = src._sent             # <<<<<<<<<<<<<<
 *         this._stack = src._stack
 *         this._rebuffer = src._rebuffer
 */
  __pyx_t_2 = __pyx_v_src->_sent;
  this->_sent = __pyx_t_2;

  /* "spacy/pipeline/_parser_internals/_state.pxd":399
 *         this.length = src.length
 *         this._sent = src._sent
 *         this._stack = src._stack             # <<<<<<<<<<<<<<
 *         this._rebuffer = src._rebuffer
 *         this._sent_starts = src._sent_starts
 */
  __pyx_t_3 = __pyx_v_src->_stack;
  this->_stack = __pyx_t_3;

  /* "spacy/pipeline/_parser_internals/_state.pxd":400
 *         this._sent = src._sent
 *         this._stack = src._stack
 *         this._rebuffer = src._rebuffer             # <<<<<<<<<<<<<<
 *         this._sent_starts = src._sent_starts
 *         this._unshiftable = src._unshiftable
 */
  __pyx_t_3 = __pyx_v_src->_rebuffer;
  this->_rebuffer = __pyx_t_3;

  /* "spacy/pipeline/_parser_internals/_state.pxd":401
 *         this._stack = src._stack
 *         this._rebuffer = src._rebuffer
 *         this._sent_starts = src._sent_starts             # <<<<<<<<<<<<<<
 *         this._unshiftable = src._unshiftable
 *         memcpy(this._heads, src._heads, this.length * sizeof(this._heads[0]))
 */
  __pyx_t_4 = __pyx_v_src->_sent_starts;
  this->_sent_starts = __pyx_t_4;

  /* "spacy/pipeline/_parser_internals/_state.pxd":402
 *         this._rebuffer = src._rebuffer
 *         this._sent_starts = src._sent_starts
 *         this._unshiftable = src._unshiftable             # <<<<<<<<<<<<<<
 *         memcpy(this._heads, src._heads, this.length * sizeof(this._heads[0]))
 *         this._ents = src._ents
 */
  __pyx_t_5 = __pyx_v_src->_unshiftable;
  this->_unshiftable = __pyx_t_5;

  /* "spacy/pipeline/_parser_internals/_state.pxd":403
 *         this._sent_starts = src._sent_starts
 *         this._unshiftable = src._unshiftable
 *         memcpy(this._heads, src._heads, this.length * sizeof(this._heads[0]))             # <<<<<<<<<<<<<<
 *         this._ents = src._ents
 *         this._left_arcs = src._left_arcs
 */
  (void)(memcpy(this->_heads, __pyx_v_src->_heads, (this->length * (sizeof((this->_heads[0]))))));

  /* "spacy/pipeline/_parser_internals/_state.pxd":404
 *         this._unshiftable = src._unshiftable
 *         memcpy(this._heads, src._heads, this.length * sizeof(this._heads[0]))
 *         this._ents = src._ents             # <<<<<<<<<<<<<<
 *         this._left_arcs = src._left_arcs
 *         this._right_arcs = src._right_arcs
 */
  __pyx_t_6 = __pyx_v_src->_ents;
  this->_ents = __pyx_t_6;

  /* "spacy/pipeline/_parser_internals/_state.pxd":405
 *         memcpy(this._heads, src._heads, this.length * sizeof(this._heads[0]))
 *         this._ents = src._ents
 *         this._left_arcs = src._left_arcs             # <<<<<<<<<<<<<<
 *         this._right_arcs = src._right_arcs
 *         this._b_i = src._b_i
 */
  __pyx_t_7 = __pyx_v_src->_left_arcs;
  this->_left_arcs = __pyx_t_7;

  /* "spacy/pipeline/_parser_internals/_state.pxd":406
 *         this._ents = src._ents
 *         this._left_arcs = src._left_arcs
 *         this._right_arcs = src._right_arcs             # <<<<<<<<<<<<<<
 *         this._b_i = src._b_i
 *         this.offset = src.offset
 */
  __pyx_t_7 = __pyx_v_src->_right_arcs;
  this->_right_arcs = __pyx_t_7;

  /* "spacy/pipeline/_parser_internals/_state.pxd":407
 *         this._left_arcs = src._left_arcs
 *         this._right_arcs = src._right_arcs
 *         this._b_i = src._b_i             # <<<<<<<<<<<<<<
 *         this.offset = src.offset
 *         this._empty_token = src._empty_token
 */
  __pyx_t_1 = __pyx_v_src->_b_i;
  this->_b_i = __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":408
 *         this._right_arcs = src._right_arcs
 *         this._b_i = src._b_i
 *         this.offset = src.offset             # <<<<<<<<<<<<<<
 *         this._empty_token = src._empty_token
 */
  __pyx_t_1 = __pyx_v_src->offset;
  this->offset = __pyx_t_1;

  /* "spacy/pipeline/_parser_internals/_state.pxd":409
 *         this._b_i = src._b_i
 *         this.offset = src.offset
 *         this._empty_token = src._empty_token             # <<<<<<<<<<<<<<
 */
  __pyx_t_8 = __pyx_v_src->_empty_token;
  this->_empty_token = __pyx_t_8;

  /* "spacy/pipeline/_parser_internals/_state.pxd":396
 *         this._ents.back().end = this.B(0)+1
 * 
 *     void clone(const StateC* src) nogil:             # <<<<<<<<<<<<<<
 *         this.length = src.length
 *         this._sent = src._sent
 */

  /* function exit code */
}

static PyMethodDef __pyx_methods[] = {
  {0, 0, 0, 0}
};

#if PY_MAJOR_VERSION >= 3
#if CYTHON_PEP489_MULTI_PHASE_INIT
static PyObject* __pyx_pymod_create(PyObject *spec, PyModuleDef *def); /*proto*/
static int __pyx_pymod_exec__state(PyObject* module); /*proto*/
static PyModuleDef_Slot __pyx_moduledef_slots[] = {
  {Py_mod_create, (void*)__pyx_pymod_create},
  {Py_mod_exec, (void*)__pyx_pymod_exec__state},
  {0, NULL}
};
#endif

static struct PyModuleDef __pyx_moduledef = {
    PyModuleDef_HEAD_INIT,
    "_state",
    0, /* m_doc */
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    0, /* m_size */
  #else
    -1, /* m_size */
  #endif
    __pyx_methods /* m_methods */,
  #if CYTHON_PEP489_MULTI_PHASE_INIT
    __pyx_moduledef_slots, /* m_slots */
  #else
    NULL, /* m_reload */
  #endif
    NULL, /* m_traverse */
    NULL, /* m_clear */
    NULL /* m_free */
};
#endif
#ifndef CYTHON_SMALL_CODE
#if defined(__clang__)
    #define CYTHON_SMALL_CODE
#elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 3))
    #define CYTHON_SMALL_CODE __attribute__((cold))
#else
    #define CYTHON_SMALL_CODE
#endif
#endif

static __Pyx_StringTabEntry __pyx_string_tab[] = {
  {&__pyx_n_s_ImportError, __pyx_k_ImportError, sizeof(__pyx_k_ImportError), 0, 0, 1, 1},
  {&__pyx_n_s_MemoryError, __pyx_k_MemoryError, sizeof(__pyx_k_MemoryError), 0, 0, 1, 1},
  {&__pyx_n_s_cline_in_traceback, __pyx_k_cline_in_traceback, sizeof(__pyx_k_cline_in_traceback), 0, 0, 1, 1},
  {&__pyx_n_s_main, __pyx_k_main, sizeof(__pyx_k_main), 0, 0, 1, 1},
  {&__pyx_n_s_name, __pyx_k_name, sizeof(__pyx_k_name), 0, 0, 1, 1},
  {&__pyx_kp_s_numpy_core_multiarray_failed_to, __pyx_k_numpy_core_multiarray_failed_to, sizeof(__pyx_k_numpy_core_multiarray_failed_to), 0, 0, 1, 0},
  {&__pyx_kp_s_numpy_core_umath_failed_to_impor, __pyx_k_numpy_core_umath_failed_to_impor, sizeof(__pyx_k_numpy_core_umath_failed_to_impor), 0, 0, 1, 0},
  {&__pyx_n_s_pyx_vtable, __pyx_k_pyx_vtable, sizeof(__pyx_k_pyx_vtable), 0, 0, 1, 1},
  {&__pyx_n_s_range, __pyx_k_range, sizeof(__pyx_k_range), 0, 0, 1, 1},
  {&__pyx_n_s_test, __pyx_k_test, sizeof(__pyx_k_test), 0, 0, 1, 1},
  {0, 0, 0, 0, 0, 0, 0}
};
static CYTHON_SMALL_CODE int __Pyx_InitCachedBuiltins(void) {
  __pyx_builtin_MemoryError = __Pyx_GetBuiltinName(__pyx_n_s_MemoryError); if (!__pyx_builtin_MemoryError) __PYX_ERR(0, 49, __pyx_L1_error)
  __pyx_builtin_range = __Pyx_GetBuiltinName(__pyx_n_s_range); if (!__pyx_builtin_range) __PYX_ERR(0, 54, __pyx_L1_error)
  __pyx_builtin_ImportError = __Pyx_GetBuiltinName(__pyx_n_s_ImportError); if (!__pyx_builtin_ImportError) __PYX_ERR(1, 941, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitCachedConstants(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_InitCachedConstants", 0);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":941
 *         __pyx_import_array()
 *     except Exception:
 *         raise ImportError("numpy.core.multiarray failed to import")             # <<<<<<<<<<<<<<
 * 
 * cdef inline int import_umath() except -1:
 */
  __pyx_tuple_ = PyTuple_Pack(1, __pyx_kp_s_numpy_core_multiarray_failed_to); if (unlikely(!__pyx_tuple_)) __PYX_ERR(1, 941, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple_);
  __Pyx_GIVEREF(__pyx_tuple_);

  /* "C:/Users/<USER>/AppData/Local/Temp/pip-build-env-a8vvuhsc/overlay/Lib/site-packages/numpy/__init__.pxd":947
 *         _import_umath()
 *     except Exception:
 *         raise ImportError("numpy.core.umath failed to import")             # <<<<<<<<<<<<<<
 * 
 * cdef inline int import_ufunc() except -1:
 */
  __pyx_tuple__2 = PyTuple_Pack(1, __pyx_kp_s_numpy_core_umath_failed_to_impor); if (unlikely(!__pyx_tuple__2)) __PYX_ERR(1, 947, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_tuple__2);
  __Pyx_GIVEREF(__pyx_tuple__2);
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_RefNannyFinishContext();
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_InitGlobals(void) {
  if (__Pyx_InitStrings(__pyx_string_tab) < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  return 0;
  __pyx_L1_error:;
  return -1;
}

static CYTHON_SMALL_CODE int __Pyx_modinit_global_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_export_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_init_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_type_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_variable_import_code(void); /*proto*/
static CYTHON_SMALL_CODE int __Pyx_modinit_function_import_code(void); /*proto*/

static int __Pyx_modinit_global_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_global_init_code", 0);
  /*--- Global init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_variable_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_export_code", 0);
  /*--- Variable export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_function_export_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_export_code", 0);
  /*--- Function export code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_init_code(void) {
  __Pyx_RefNannyDeclarations
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_init_code", 0);
  /*--- Type init code ---*/
  __Pyx_RefNannyFinishContext();
  return 0;
}

static int __Pyx_modinit_type_import_code(void) {
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("__Pyx_modinit_type_import_code", 0);
  /*--- Type import code ---*/
  __pyx_t_1 = PyImport_ImportModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_t_1)) __PYX_ERR(4, 9, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_7cpython_4type_type = __Pyx_ImportType_0_29_36(__pyx_t_1, __Pyx_BUILTIN_MODULE_NAME, "type", 
  #if defined(PYPY_VERSION_NUM) && PYPY_VERSION_NUM < 0x050B0000
  sizeof(PyTypeObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyTypeObject),
  #else
  sizeof(PyHeapTypeObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyHeapTypeObject),
  #endif
  __Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_7cpython_4type_type) __PYX_ERR(4, 9, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("numpy"); if (unlikely(!__pyx_t_1)) __PYX_ERR(1, 199, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5numpy_dtype = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "dtype", sizeof(PyArray_Descr), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyArray_Descr),__Pyx_ImportType_CheckSize_Ignore_0_29_36); if (!__pyx_ptype_5numpy_dtype) __PYX_ERR(1, 199, __pyx_L1_error)
  __pyx_ptype_5numpy_flatiter = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "flatiter", sizeof(PyArrayIterObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyArrayIterObject),__Pyx_ImportType_CheckSize_Ignore_0_29_36); if (!__pyx_ptype_5numpy_flatiter) __PYX_ERR(1, 222, __pyx_L1_error)
  __pyx_ptype_5numpy_broadcast = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "broadcast", sizeof(PyArrayMultiIterObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyArrayMultiIterObject),__Pyx_ImportType_CheckSize_Ignore_0_29_36); if (!__pyx_ptype_5numpy_broadcast) __PYX_ERR(1, 226, __pyx_L1_error)
  __pyx_ptype_5numpy_ndarray = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "ndarray", sizeof(PyArrayObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyArrayObject),__Pyx_ImportType_CheckSize_Ignore_0_29_36); if (!__pyx_ptype_5numpy_ndarray) __PYX_ERR(1, 238, __pyx_L1_error)
  __pyx_ptype_5numpy_generic = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "generic", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_generic) __PYX_ERR(1, 767, __pyx_L1_error)
  __pyx_ptype_5numpy_number = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "number", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_number) __PYX_ERR(1, 769, __pyx_L1_error)
  __pyx_ptype_5numpy_integer = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "integer", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_integer) __PYX_ERR(1, 771, __pyx_L1_error)
  __pyx_ptype_5numpy_signedinteger = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "signedinteger", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_signedinteger) __PYX_ERR(1, 773, __pyx_L1_error)
  __pyx_ptype_5numpy_unsignedinteger = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "unsignedinteger", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_unsignedinteger) __PYX_ERR(1, 775, __pyx_L1_error)
  __pyx_ptype_5numpy_inexact = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "inexact", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_inexact) __PYX_ERR(1, 777, __pyx_L1_error)
  __pyx_ptype_5numpy_floating = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "floating", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_floating) __PYX_ERR(1, 779, __pyx_L1_error)
  __pyx_ptype_5numpy_complexfloating = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "complexfloating", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_complexfloating) __PYX_ERR(1, 781, __pyx_L1_error)
  __pyx_ptype_5numpy_flexible = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "flexible", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_flexible) __PYX_ERR(1, 783, __pyx_L1_error)
  __pyx_ptype_5numpy_character = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "character", sizeof(PyObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyObject),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5numpy_character) __PYX_ERR(1, 785, __pyx_L1_error)
  __pyx_ptype_5numpy_ufunc = __Pyx_ImportType_0_29_36(__pyx_t_1, "numpy", "ufunc", sizeof(PyUFuncObject), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(PyUFuncObject),__Pyx_ImportType_CheckSize_Ignore_0_29_36); if (!__pyx_ptype_5numpy_ufunc) __PYX_ERR(1, 823, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("cymem.cymem"); if (unlikely(!__pyx_t_1)) __PYX_ERR(5, 4, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5cymem_5cymem_PyMalloc = __Pyx_ImportType_0_29_36(__pyx_t_1, "cymem.cymem", "PyMalloc", sizeof(struct __pyx_obj_5cymem_5cymem_PyMalloc), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5cymem_5cymem_PyMalloc),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5cymem_5cymem_PyMalloc) __PYX_ERR(5, 4, __pyx_L1_error)
  __pyx_vtabptr_5cymem_5cymem_PyMalloc = (struct __pyx_vtabstruct_5cymem_5cymem_PyMalloc*)__Pyx_GetVtable(__pyx_ptype_5cymem_5cymem_PyMalloc->tp_dict); if (unlikely(!__pyx_vtabptr_5cymem_5cymem_PyMalloc)) __PYX_ERR(5, 4, __pyx_L1_error)
  __pyx_ptype_5cymem_5cymem_PyFree = __Pyx_ImportType_0_29_36(__pyx_t_1, "cymem.cymem", "PyFree", sizeof(struct __pyx_obj_5cymem_5cymem_PyFree), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5cymem_5cymem_PyFree),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5cymem_5cymem_PyFree) __PYX_ERR(5, 10, __pyx_L1_error)
  __pyx_vtabptr_5cymem_5cymem_PyFree = (struct __pyx_vtabstruct_5cymem_5cymem_PyFree*)__Pyx_GetVtable(__pyx_ptype_5cymem_5cymem_PyFree->tp_dict); if (unlikely(!__pyx_vtabptr_5cymem_5cymem_PyFree)) __PYX_ERR(5, 10, __pyx_L1_error)
  __pyx_ptype_5cymem_5cymem_Pool = __Pyx_ImportType_0_29_36(__pyx_t_1, "cymem.cymem", "Pool", sizeof(struct __pyx_obj_5cymem_5cymem_Pool), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5cymem_5cymem_Pool),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5cymem_5cymem_Pool) __PYX_ERR(5, 16, __pyx_L1_error)
  __pyx_vtabptr_5cymem_5cymem_Pool = (struct __pyx_vtabstruct_5cymem_5cymem_Pool*)__Pyx_GetVtable(__pyx_ptype_5cymem_5cymem_Pool->tp_dict); if (unlikely(!__pyx_vtabptr_5cymem_5cymem_Pool)) __PYX_ERR(5, 16, __pyx_L1_error)
  __pyx_ptype_5cymem_5cymem_Address = __Pyx_ImportType_0_29_36(__pyx_t_1, "cymem.cymem", "Address", sizeof(struct __pyx_obj_5cymem_5cymem_Address), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5cymem_5cymem_Address),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5cymem_5cymem_Address) __PYX_ERR(5, 28, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("preshed.maps"); if (unlikely(!__pyx_t_1)) __PYX_ERR(6, 45, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_7preshed_4maps_PreshMap = __Pyx_ImportType_0_29_36(__pyx_t_1, "preshed.maps", "PreshMap", sizeof(struct __pyx_obj_7preshed_4maps_PreshMap), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_7preshed_4maps_PreshMap),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_7preshed_4maps_PreshMap) __PYX_ERR(6, 45, __pyx_L1_error)
  __pyx_vtabptr_7preshed_4maps_PreshMap = (struct __pyx_vtabstruct_7preshed_4maps_PreshMap*)__Pyx_GetVtable(__pyx_ptype_7preshed_4maps_PreshMap->tp_dict); if (unlikely(!__pyx_vtabptr_7preshed_4maps_PreshMap)) __PYX_ERR(6, 45, __pyx_L1_error)
  __pyx_ptype_7preshed_4maps_PreshMapArray = __Pyx_ImportType_0_29_36(__pyx_t_1, "preshed.maps", "PreshMapArray", sizeof(struct __pyx_obj_7preshed_4maps_PreshMapArray), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_7preshed_4maps_PreshMapArray),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_7preshed_4maps_PreshMapArray) __PYX_ERR(6, 53, __pyx_L1_error)
  __pyx_vtabptr_7preshed_4maps_PreshMapArray = (struct __pyx_vtabstruct_7preshed_4maps_PreshMapArray*)__Pyx_GetVtable(__pyx_ptype_7preshed_4maps_PreshMapArray->tp_dict); if (unlikely(!__pyx_vtabptr_7preshed_4maps_PreshMapArray)) __PYX_ERR(6, 53, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("spacy.strings"); if (unlikely(!__pyx_t_1)) __PYX_ERR(7, 22, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5spacy_7strings_StringStore = __Pyx_ImportType_0_29_36(__pyx_t_1, "spacy.strings", "StringStore", sizeof(struct __pyx_obj_5spacy_7strings_StringStore), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5spacy_7strings_StringStore),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5spacy_7strings_StringStore) __PYX_ERR(7, 22, __pyx_L1_error)
  __pyx_vtabptr_5spacy_7strings_StringStore = (struct __pyx_vtabstruct_5spacy_7strings_StringStore*)__Pyx_GetVtable(__pyx_ptype_5spacy_7strings_StringStore->tp_dict); if (unlikely(!__pyx_vtabptr_5spacy_7strings_StringStore)) __PYX_ERR(7, 22, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("spacy.morphology"); if (unlikely(!__pyx_t_1)) __PYX_ERR(8, 11, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5spacy_10morphology_Morphology = __Pyx_ImportType_0_29_36(__pyx_t_1, "spacy.morphology", "Morphology", sizeof(struct __pyx_obj_5spacy_10morphology_Morphology), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5spacy_10morphology_Morphology),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5spacy_10morphology_Morphology) __PYX_ERR(8, 11, __pyx_L1_error)
  __pyx_vtabptr_5spacy_10morphology_Morphology = (struct __pyx_vtabstruct_5spacy_10morphology_Morphology*)__Pyx_GetVtable(__pyx_ptype_5spacy_10morphology_Morphology->tp_dict); if (unlikely(!__pyx_vtabptr_5spacy_10morphology_Morphology)) __PYX_ERR(8, 11, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("spacy.vocab"); if (unlikely(!__pyx_t_1)) __PYX_ERR(9, 26, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5spacy_5vocab_Vocab = __Pyx_ImportType_0_29_36(__pyx_t_1, "spacy.vocab", "Vocab", sizeof(struct __pyx_obj_5spacy_5vocab_Vocab), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5spacy_5vocab_Vocab),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5spacy_5vocab_Vocab) __PYX_ERR(9, 26, __pyx_L1_error)
  __pyx_vtabptr_5spacy_5vocab_Vocab = (struct __pyx_vtabstruct_5spacy_5vocab_Vocab*)__Pyx_GetVtable(__pyx_ptype_5spacy_5vocab_Vocab->tp_dict); if (unlikely(!__pyx_vtabptr_5spacy_5vocab_Vocab)) __PYX_ERR(9, 26, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("spacy.lexeme"); if (unlikely(!__pyx_t_1)) __PYX_ERR(2, 24, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  __pyx_ptype_5spacy_6lexeme_Lexeme = __Pyx_ImportType_0_29_36(__pyx_t_1, "spacy.lexeme", "Lexeme", sizeof(struct __pyx_obj_5spacy_6lexeme_Lexeme), __PYX_GET_STRUCT_ALIGNMENT_0_29_36(struct __pyx_obj_5spacy_6lexeme_Lexeme),__Pyx_ImportType_CheckSize_Warn_0_29_36); if (!__pyx_ptype_5spacy_6lexeme_Lexeme) __PYX_ERR(2, 24, __pyx_L1_error)
  __pyx_vtabptr_5spacy_6lexeme_Lexeme = (struct __pyx_vtabstruct_5spacy_6lexeme_Lexeme*)__Pyx_GetVtable(__pyx_ptype_5spacy_6lexeme_Lexeme->tp_dict); if (unlikely(!__pyx_vtabptr_5spacy_6lexeme_Lexeme)) __PYX_ERR(2, 24, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_RefNannyFinishContext();
  return -1;
}

static int __Pyx_modinit_variable_import_code(void) {
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("__Pyx_modinit_variable_import_code", 0);
  /*--- Variable import code ---*/
  __pyx_t_1 = PyImport_ImportModule("spacy.vocab"); if (!__pyx_t_1) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (__Pyx_ImportVoidPtr_0_29_36(__pyx_t_1, "EMPTY_LEXEME", (void **)&__pyx_vp_5spacy_5vocab_EMPTY_LEXEME, "struct __pyx_t_5spacy_7structs_LexemeC") < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __pyx_t_1 = PyImport_ImportModule("spacy.lexeme"); if (!__pyx_t_1) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (__Pyx_ImportVoidPtr_0_29_36(__pyx_t_1, "EMPTY_LEXEME", (void **)&__pyx_vp_5spacy_6lexeme_EMPTY_LEXEME, "struct __pyx_t_5spacy_7structs_LexemeC") < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  if (__Pyx_ImportVoidPtr_0_29_36(__pyx_t_1, "OOV_RANK", (void **)&__pyx_vp_5spacy_6lexeme_OOV_RANK, "__pyx_t_5spacy_8typedefs_attr_t") < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_RefNannyFinishContext();
  return -1;
}

static int __Pyx_modinit_function_import_code(void) {
  __Pyx_RefNannyDeclarations
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannySetupContext("__Pyx_modinit_function_import_code", 0);
  /*--- Function import code ---*/
  __pyx_t_1 = PyImport_ImportModule("murmurhash.mrmr"); if (!__pyx_t_1) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (__Pyx_ImportFunction_0_29_36(__pyx_t_1, "hash64", (void (**)(void))&__pyx_f_10murmurhash_4mrmr_hash64, "uint64_t (void *, int, uint64_t)") < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;
  __Pyx_RefNannyFinishContext();
  return 0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  __Pyx_RefNannyFinishContext();
  return -1;
}


#ifndef CYTHON_NO_PYINIT_EXPORT
#define __Pyx_PyMODINIT_FUNC PyMODINIT_FUNC
#elif PY_MAJOR_VERSION < 3
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" void
#else
#define __Pyx_PyMODINIT_FUNC void
#endif
#else
#ifdef __cplusplus
#define __Pyx_PyMODINIT_FUNC extern "C" PyObject *
#else
#define __Pyx_PyMODINIT_FUNC PyObject *
#endif
#endif


#if PY_MAJOR_VERSION < 3
__Pyx_PyMODINIT_FUNC init_state(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC init_state(void)
#else
__Pyx_PyMODINIT_FUNC PyInit__state(void) CYTHON_SMALL_CODE; /*proto*/
__Pyx_PyMODINIT_FUNC PyInit__state(void)
#if CYTHON_PEP489_MULTI_PHASE_INIT
{
  return PyModuleDef_Init(&__pyx_moduledef);
}
static CYTHON_SMALL_CODE int __Pyx_check_single_interpreter(void) {
    #if PY_VERSION_HEX >= 0x030700A1
    static PY_INT64_T main_interpreter_id = -1;
    PY_INT64_T current_id = PyInterpreterState_GetID(PyThreadState_Get()->interp);
    if (main_interpreter_id == -1) {
        main_interpreter_id = current_id;
        return (unlikely(current_id == -1)) ? -1 : 0;
    } else if (unlikely(main_interpreter_id != current_id))
    #else
    static PyInterpreterState *main_interpreter = NULL;
    PyInterpreterState *current_interpreter = PyThreadState_Get()->interp;
    if (!main_interpreter) {
        main_interpreter = current_interpreter;
    } else if (unlikely(main_interpreter != current_interpreter))
    #endif
    {
        PyErr_SetString(
            PyExc_ImportError,
            "Interpreter change detected - this module can only be loaded into one interpreter per process.");
        return -1;
    }
    return 0;
}
static CYTHON_SMALL_CODE int __Pyx_copy_spec_to_module(PyObject *spec, PyObject *moddict, const char* from_name, const char* to_name, int allow_none) {
    PyObject *value = PyObject_GetAttrString(spec, from_name);
    int result = 0;
    if (likely(value)) {
        if (allow_none || value != Py_None) {
            result = PyDict_SetItemString(moddict, to_name, value);
        }
        Py_DECREF(value);
    } else if (PyErr_ExceptionMatches(PyExc_AttributeError)) {
        PyErr_Clear();
    } else {
        result = -1;
    }
    return result;
}
static CYTHON_SMALL_CODE PyObject* __pyx_pymod_create(PyObject *spec, CYTHON_UNUSED PyModuleDef *def) {
    PyObject *module = NULL, *moddict, *modname;
    if (__Pyx_check_single_interpreter())
        return NULL;
    if (__pyx_m)
        return __Pyx_NewRef(__pyx_m);
    modname = PyObject_GetAttrString(spec, "name");
    if (unlikely(!modname)) goto bad;
    module = PyModule_NewObject(modname);
    Py_DECREF(modname);
    if (unlikely(!module)) goto bad;
    moddict = PyModule_GetDict(module);
    if (unlikely(!moddict)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "loader", "__loader__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "origin", "__file__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "parent", "__package__", 1) < 0)) goto bad;
    if (unlikely(__Pyx_copy_spec_to_module(spec, moddict, "submodule_search_locations", "__path__", 0) < 0)) goto bad;
    return module;
bad:
    Py_XDECREF(module);
    return NULL;
}


static CYTHON_SMALL_CODE int __pyx_pymod_exec__state(PyObject *__pyx_pyinit_module)
#endif
#endif
{
  PyObject *__pyx_t_1 = NULL;
  int __pyx_lineno = 0;
  const char *__pyx_filename = NULL;
  int __pyx_clineno = 0;
  __Pyx_RefNannyDeclarations
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  if (__pyx_m) {
    if (__pyx_m == __pyx_pyinit_module) return 0;
    PyErr_SetString(PyExc_RuntimeError, "Module '_state' has already been imported. Re-initialisation is not supported.");
    return -1;
  }
  #elif PY_MAJOR_VERSION >= 3
  if (__pyx_m) return __Pyx_NewRef(__pyx_m);
  #endif
  #if CYTHON_REFNANNY
__Pyx_RefNanny = __Pyx_RefNannyImportAPI("refnanny");
if (!__Pyx_RefNanny) {
  PyErr_Clear();
  __Pyx_RefNanny = __Pyx_RefNannyImportAPI("Cython.Runtime.refnanny");
  if (!__Pyx_RefNanny)
      Py_FatalError("failed to import 'refnanny' module");
}
#endif
  __Pyx_RefNannySetupContext("__Pyx_PyMODINIT_FUNC PyInit__state(void)", 0);
  if (__Pyx_check_binary_version() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #ifdef __Pxy_PyFrame_Initialize_Offsets
  __Pxy_PyFrame_Initialize_Offsets();
  #endif
  __pyx_empty_tuple = PyTuple_New(0); if (unlikely(!__pyx_empty_tuple)) __PYX_ERR(3, 1, __pyx_L1_error)
  __pyx_empty_bytes = PyBytes_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_bytes)) __PYX_ERR(3, 1, __pyx_L1_error)
  __pyx_empty_unicode = PyUnicode_FromStringAndSize("", 0); if (unlikely(!__pyx_empty_unicode)) __PYX_ERR(3, 1, __pyx_L1_error)
  #ifdef __Pyx_CyFunction_USED
  if (__pyx_CyFunction_init() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_FusedFunction_USED
  if (__pyx_FusedFunction_init() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Coroutine_USED
  if (__pyx_Coroutine_init() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_Generator_USED
  if (__pyx_Generator_init() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_AsyncGen_USED
  if (__pyx_AsyncGen_init() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  #ifdef __Pyx_StopAsyncIteration_USED
  if (__pyx_StopAsyncIteration_init() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  /*--- Library function declarations ---*/
  /*--- Threads initialization code ---*/
  #if defined(WITH_THREAD) && PY_VERSION_HEX < 0x030700F0 && defined(__PYX_FORCE_INIT_THREADS) && __PYX_FORCE_INIT_THREADS
  PyEval_InitThreads();
  #endif
  /*--- Module creation code ---*/
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  __pyx_m = __pyx_pyinit_module;
  Py_INCREF(__pyx_m);
  #else
  #if PY_MAJOR_VERSION < 3
  __pyx_m = Py_InitModule4("_state", __pyx_methods, 0, 0, PYTHON_API_VERSION); Py_XINCREF(__pyx_m);
  #else
  __pyx_m = PyModule_Create(&__pyx_moduledef);
  #endif
  if (unlikely(!__pyx_m)) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  __pyx_d = PyModule_GetDict(__pyx_m); if (unlikely(!__pyx_d)) __PYX_ERR(3, 1, __pyx_L1_error)
  Py_INCREF(__pyx_d);
  __pyx_b = PyImport_AddModule(__Pyx_BUILTIN_MODULE_NAME); if (unlikely(!__pyx_b)) __PYX_ERR(3, 1, __pyx_L1_error)
  Py_INCREF(__pyx_b);
  __pyx_cython_runtime = PyImport_AddModule((char *) "cython_runtime"); if (unlikely(!__pyx_cython_runtime)) __PYX_ERR(3, 1, __pyx_L1_error)
  Py_INCREF(__pyx_cython_runtime);
  if (PyObject_SetAttrString(__pyx_m, "__builtins__", __pyx_b) < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  /*--- Initialize various global constants etc. ---*/
  if (__Pyx_InitGlobals() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #if PY_MAJOR_VERSION < 3 && (__PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT)
  if (__Pyx_init_sys_getdefaultencoding_params() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif
  if (__pyx_module_is_main_spacy__pipeline___parser_internals___state) {
    if (PyObject_SetAttr(__pyx_m, __pyx_n_s_name, __pyx_n_s_main) < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  }
  #if PY_MAJOR_VERSION >= 3
  {
    PyObject *modules = PyImport_GetModuleDict(); if (unlikely(!modules)) __PYX_ERR(3, 1, __pyx_L1_error)
    if (!PyDict_GetItemString(modules, "spacy.pipeline._parser_internals._state")) {
      if (unlikely(PyDict_SetItemString(modules, "spacy.pipeline._parser_internals._state", __pyx_m) < 0)) __PYX_ERR(3, 1, __pyx_L1_error)
    }
  }
  #endif
  /*--- Builtin init code ---*/
  if (__Pyx_InitCachedBuiltins() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  /*--- Constants init code ---*/
  if (__Pyx_InitCachedConstants() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  /*--- Global type/function init code ---*/
  (void)__Pyx_modinit_global_init_code();
  (void)__Pyx_modinit_variable_export_code();
  (void)__Pyx_modinit_function_export_code();
  (void)__Pyx_modinit_type_init_code();
  if (unlikely(__Pyx_modinit_type_import_code() < 0)) __PYX_ERR(3, 1, __pyx_L1_error)
  if (unlikely(__Pyx_modinit_variable_import_code() < 0)) __PYX_ERR(3, 1, __pyx_L1_error)
  if (unlikely(__Pyx_modinit_function_import_code() < 0)) __PYX_ERR(3, 1, __pyx_L1_error)
  /*--- Execution code ---*/
  #if defined(__Pyx_Generator_USED) || defined(__Pyx_Coroutine_USED)
  if (__Pyx_patch_abc() < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  #endif

  /* "spacy/pipeline/_parser_internals/_state.pyx":1
 * # cython: profile=False             # <<<<<<<<<<<<<<
 */
  __pyx_t_1 = __Pyx_PyDict_NewPresized(0); if (unlikely(!__pyx_t_1)) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_GOTREF(__pyx_t_1);
  if (PyDict_SetItem(__pyx_d, __pyx_n_s_test, __pyx_t_1) < 0) __PYX_ERR(3, 1, __pyx_L1_error)
  __Pyx_DECREF(__pyx_t_1); __pyx_t_1 = 0;

  /* "spacy/pipeline/_parser_internals/_state.pxd":396
 *         this._ents.back().end = this.B(0)+1
 * 
 *     void clone(const StateC* src) nogil:             # <<<<<<<<<<<<<<
 *         this.length = src.length
 *         this._sent = src._sent
 */

  /*--- Wrapped vars code ---*/

  goto __pyx_L0;
  __pyx_L1_error:;
  __Pyx_XDECREF(__pyx_t_1);
  if (__pyx_m) {
    if (__pyx_d) {
      __Pyx_AddTraceback("init spacy.pipeline._parser_internals._state", __pyx_clineno, __pyx_lineno, __pyx_filename);
    }
    Py_CLEAR(__pyx_m);
  } else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_ImportError, "init spacy.pipeline._parser_internals._state");
  }
  __pyx_L0:;
  __Pyx_RefNannyFinishContext();
  #if CYTHON_PEP489_MULTI_PHASE_INIT
  return (__pyx_m != NULL) ? 0 : -1;
  #elif PY_MAJOR_VERSION >= 3
  return __pyx_m;
  #else
  return;
  #endif
}

/* --- Runtime support code --- */
/* Refnanny */
#if CYTHON_REFNANNY
static __Pyx_RefNannyAPIStruct *__Pyx_RefNannyImportAPI(const char *modname) {
    PyObject *m = NULL, *p = NULL;
    void *r = NULL;
    m = PyImport_ImportModule(modname);
    if (!m) goto end;
    p = PyObject_GetAttrString(m, "RefNannyAPI");
    if (!p) goto end;
    r = PyLong_AsVoidPtr(p);
end:
    Py_XDECREF(p);
    Py_XDECREF(m);
    return (__Pyx_RefNannyAPIStruct *)r;
}
#endif

/* PyObjectGetAttrStr */
#if CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PyObject* __Pyx_PyObject_GetAttrStr(PyObject* obj, PyObject* attr_name) {
    PyTypeObject* tp = Py_TYPE(obj);
    if (likely(tp->tp_getattro))
        return tp->tp_getattro(obj, attr_name);
#if PY_MAJOR_VERSION < 3
    if (likely(tp->tp_getattr))
        return tp->tp_getattr(obj, PyString_AS_STRING(attr_name));
#endif
    return PyObject_GetAttr(obj, attr_name);
}
#endif

/* GetBuiltinName */
static PyObject *__Pyx_GetBuiltinName(PyObject *name) {
    PyObject* result = __Pyx_PyObject_GetAttrStr(__pyx_b, name);
    if (unlikely(!result)) {
        PyErr_Format(PyExc_NameError,
#if PY_MAJOR_VERSION >= 3
            "name '%U' is not defined", name);
#else
            "name '%.200s' is not defined", PyString_AS_STRING(name));
#endif
    }
    return result;
}

/* PyErrFetchRestore */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx_ErrRestoreInState(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    tmp_type = tstate->curexc_type;
    tmp_value = tstate->curexc_value;
    tmp_tb = tstate->curexc_traceback;
    tstate->curexc_type = type;
    tstate->curexc_value = value;
    tstate->curexc_traceback = tb;
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
static CYTHON_INLINE void __Pyx_ErrFetchInState(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    *type = tstate->curexc_type;
    *value = tstate->curexc_value;
    *tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
}
#endif

/* WriteUnraisableException */
static void __Pyx_WriteUnraisable(const char *name, CYTHON_UNUSED int clineno,
                                  CYTHON_UNUSED int lineno, CYTHON_UNUSED const char *filename,
                                  int full_traceback, CYTHON_UNUSED int nogil) {
    PyObject *old_exc, *old_val, *old_tb;
    PyObject *ctx;
    __Pyx_PyThreadState_declare
#ifdef WITH_THREAD
    PyGILState_STATE state;
    if (nogil)
        state = PyGILState_Ensure();
    else state = (PyGILState_STATE)0;
#endif
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&old_exc, &old_val, &old_tb);
    if (full_traceback) {
        Py_XINCREF(old_exc);
        Py_XINCREF(old_val);
        Py_XINCREF(old_tb);
        __Pyx_ErrRestore(old_exc, old_val, old_tb);
        PyErr_PrintEx(1);
    }
    #if PY_MAJOR_VERSION < 3
    ctx = PyString_FromString(name);
    #else
    ctx = PyUnicode_FromString(name);
    #endif
    __Pyx_ErrRestore(old_exc, old_val, old_tb);
    if (!ctx) {
        PyErr_WriteUnraisable(Py_None);
    } else {
        PyErr_WriteUnraisable(ctx);
        Py_DECREF(ctx);
    }
#ifdef WITH_THREAD
    if (nogil)
        PyGILState_Release(state);
#endif
}

/* GetTopmostException */
#if CYTHON_USE_EXC_INFO_STACK
static _PyErr_StackItem *
__Pyx_PyErr_GetTopmostException(PyThreadState *tstate)
{
    _PyErr_StackItem *exc_info = tstate->exc_info;
    while ((exc_info->exc_type == NULL || exc_info->exc_type == Py_None) &&
           exc_info->previous_item != NULL)
    {
        exc_info = exc_info->previous_item;
    }
    return exc_info;
}
#endif

/* SaveResetException */
#if CYTHON_FAST_THREAD_STATE
static CYTHON_INLINE void __Pyx__ExceptionSave(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb) {
    #if CYTHON_USE_EXC_INFO_STACK
    _PyErr_StackItem *exc_info = __Pyx_PyErr_GetTopmostException(tstate);
    *type = exc_info->exc_type;
    *value = exc_info->exc_value;
    *tb = exc_info->exc_traceback;
    #else
    *type = tstate->exc_type;
    *value = tstate->exc_value;
    *tb = tstate->exc_traceback;
    #endif
    Py_XINCREF(*type);
    Py_XINCREF(*value);
    Py_XINCREF(*tb);
}
static CYTHON_INLINE void __Pyx__ExceptionReset(PyThreadState *tstate, PyObject *type, PyObject *value, PyObject *tb) {
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    #if CYTHON_USE_EXC_INFO_STACK
    _PyErr_StackItem *exc_info = tstate->exc_info;
    tmp_type = exc_info->exc_type;
    tmp_value = exc_info->exc_value;
    tmp_tb = exc_info->exc_traceback;
    exc_info->exc_type = type;
    exc_info->exc_value = value;
    exc_info->exc_traceback = tb;
    #else
    tmp_type = tstate->exc_type;
    tmp_value = tstate->exc_value;
    tmp_tb = tstate->exc_traceback;
    tstate->exc_type = type;
    tstate->exc_value = value;
    tstate->exc_traceback = tb;
    #endif
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
}
#endif

/* PyErrExceptionMatches */
#if CYTHON_FAST_THREAD_STATE
static int __Pyx_PyErr_ExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        if (__Pyx_PyErr_GivenExceptionMatches(exc_type, PyTuple_GET_ITEM(tuple, i))) return 1;
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_ExceptionMatchesInState(PyThreadState* tstate, PyObject* err) {
    PyObject *exc_type = tstate->curexc_type;
    if (exc_type == err) return 1;
    if (unlikely(!exc_type)) return 0;
    if (unlikely(PyTuple_Check(err)))
        return __Pyx_PyErr_ExceptionMatchesTuple(exc_type, err);
    return __Pyx_PyErr_GivenExceptionMatches(exc_type, err);
}
#endif

/* GetException */
#if CYTHON_FAST_THREAD_STATE
static int __Pyx__GetException(PyThreadState *tstate, PyObject **type, PyObject **value, PyObject **tb)
#else
static int __Pyx_GetException(PyObject **type, PyObject **value, PyObject **tb)
#endif
{
    PyObject *local_type, *local_value, *local_tb;
#if CYTHON_FAST_THREAD_STATE
    PyObject *tmp_type, *tmp_value, *tmp_tb;
    local_type = tstate->curexc_type;
    local_value = tstate->curexc_value;
    local_tb = tstate->curexc_traceback;
    tstate->curexc_type = 0;
    tstate->curexc_value = 0;
    tstate->curexc_traceback = 0;
#else
    PyErr_Fetch(&local_type, &local_value, &local_tb);
#endif
    PyErr_NormalizeException(&local_type, &local_value, &local_tb);
#if CYTHON_FAST_THREAD_STATE
    if (unlikely(tstate->curexc_type))
#else
    if (unlikely(PyErr_Occurred()))
#endif
        goto bad;
    #if PY_MAJOR_VERSION >= 3
    if (local_tb) {
        if (unlikely(PyException_SetTraceback(local_value, local_tb) < 0))
            goto bad;
    }
    #endif
    Py_XINCREF(local_tb);
    Py_XINCREF(local_type);
    Py_XINCREF(local_value);
    *type = local_type;
    *value = local_value;
    *tb = local_tb;
#if CYTHON_FAST_THREAD_STATE
    #if CYTHON_USE_EXC_INFO_STACK
    {
        _PyErr_StackItem *exc_info = tstate->exc_info;
        tmp_type = exc_info->exc_type;
        tmp_value = exc_info->exc_value;
        tmp_tb = exc_info->exc_traceback;
        exc_info->exc_type = local_type;
        exc_info->exc_value = local_value;
        exc_info->exc_traceback = local_tb;
    }
    #else
    tmp_type = tstate->exc_type;
    tmp_value = tstate->exc_value;
    tmp_tb = tstate->exc_traceback;
    tstate->exc_type = local_type;
    tstate->exc_value = local_value;
    tstate->exc_traceback = local_tb;
    #endif
    Py_XDECREF(tmp_type);
    Py_XDECREF(tmp_value);
    Py_XDECREF(tmp_tb);
#else
    PyErr_SetExcInfo(local_type, local_value, local_tb);
#endif
    return 0;
bad:
    *type = 0;
    *value = 0;
    *tb = 0;
    Py_XDECREF(local_type);
    Py_XDECREF(local_value);
    Py_XDECREF(local_tb);
    return -1;
}

/* PyObjectCall */
#if CYTHON_COMPILING_IN_CPYTHON
static CYTHON_INLINE PyObject* __Pyx_PyObject_Call(PyObject *func, PyObject *arg, PyObject *kw) {
    PyObject *result;
    ternaryfunc call = Py_TYPE(func)->tp_call;
    if (unlikely(!call))
        return PyObject_Call(func, arg, kw);
    if (unlikely(Py_EnterRecursiveCall((char*)" while calling a Python object")))
        return NULL;
    result = (*call)(func, arg, kw);
    Py_LeaveRecursiveCall();
    if (unlikely(!result) && unlikely(!PyErr_Occurred())) {
        PyErr_SetString(
            PyExc_SystemError,
            "NULL result without error in PyObject_Call");
    }
    return result;
}
#endif

/* RaiseException */
#if PY_MAJOR_VERSION < 3
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb,
                        CYTHON_UNUSED PyObject *cause) {
    __Pyx_PyThreadState_declare
    Py_XINCREF(type);
    if (!value || value == Py_None)
        value = NULL;
    else
        Py_INCREF(value);
    if (!tb || tb == Py_None)
        tb = NULL;
    else {
        Py_INCREF(tb);
        if (!PyTraceBack_Check(tb)) {
            PyErr_SetString(PyExc_TypeError,
                "raise: arg 3 must be a traceback or None");
            goto raise_error;
        }
    }
    if (PyType_Check(type)) {
#if CYTHON_COMPILING_IN_PYPY
        if (!value) {
            Py_INCREF(Py_None);
            value = Py_None;
        }
#endif
        PyErr_NormalizeException(&type, &value, &tb);
    } else {
        if (value) {
            PyErr_SetString(PyExc_TypeError,
                "instance exception may not have a separate value");
            goto raise_error;
        }
        value = type;
        type = (PyObject*) Py_TYPE(type);
        Py_INCREF(type);
        if (!PyType_IsSubtype((PyTypeObject *)type, (PyTypeObject *)PyExc_BaseException)) {
            PyErr_SetString(PyExc_TypeError,
                "raise: exception class must be a subclass of BaseException");
            goto raise_error;
        }
    }
    __Pyx_PyThreadState_assign
    __Pyx_ErrRestore(type, value, tb);
    return;
raise_error:
    Py_XDECREF(value);
    Py_XDECREF(type);
    Py_XDECREF(tb);
    return;
}
#else
static void __Pyx_Raise(PyObject *type, PyObject *value, PyObject *tb, PyObject *cause) {
    PyObject* owned_instance = NULL;
    if (tb == Py_None) {
        tb = 0;
    } else if (tb && !PyTraceBack_Check(tb)) {
        PyErr_SetString(PyExc_TypeError,
            "raise: arg 3 must be a traceback or None");
        goto bad;
    }
    if (value == Py_None)
        value = 0;
    if (PyExceptionInstance_Check(type)) {
        if (value) {
            PyErr_SetString(PyExc_TypeError,
                "instance exception may not have a separate value");
            goto bad;
        }
        value = type;
        type = (PyObject*) Py_TYPE(value);
    } else if (PyExceptionClass_Check(type)) {
        PyObject *instance_class = NULL;
        if (value && PyExceptionInstance_Check(value)) {
            instance_class = (PyObject*) Py_TYPE(value);
            if (instance_class != type) {
                int is_subclass = PyObject_IsSubclass(instance_class, type);
                if (!is_subclass) {
                    instance_class = NULL;
                } else if (unlikely(is_subclass == -1)) {
                    goto bad;
                } else {
                    type = instance_class;
                }
            }
        }
        if (!instance_class) {
            PyObject *args;
            if (!value)
                args = PyTuple_New(0);
            else if (PyTuple_Check(value)) {
                Py_INCREF(value);
                args = value;
            } else
                args = PyTuple_Pack(1, value);
            if (!args)
                goto bad;
            owned_instance = PyObject_Call(type, args, NULL);
            Py_DECREF(args);
            if (!owned_instance)
                goto bad;
            value = owned_instance;
            if (!PyExceptionInstance_Check(value)) {
                PyErr_Format(PyExc_TypeError,
                             "calling %R should have returned an instance of "
                             "BaseException, not %R",
                             type, Py_TYPE(value));
                goto bad;
            }
        }
    } else {
        PyErr_SetString(PyExc_TypeError,
            "raise: exception class must be a subclass of BaseException");
        goto bad;
    }
    if (cause) {
        PyObject *fixed_cause;
        if (cause == Py_None) {
            fixed_cause = NULL;
        } else if (PyExceptionClass_Check(cause)) {
            fixed_cause = PyObject_CallObject(cause, NULL);
            if (fixed_cause == NULL)
                goto bad;
        } else if (PyExceptionInstance_Check(cause)) {
            fixed_cause = cause;
            Py_INCREF(fixed_cause);
        } else {
            PyErr_SetString(PyExc_TypeError,
                            "exception causes must derive from "
                            "BaseException");
            goto bad;
        }
        PyException_SetCause(value, fixed_cause);
    }
    PyErr_SetObject(type, value);
    if (tb) {
#if CYTHON_FAST_THREAD_STATE
        PyThreadState *tstate = __Pyx_PyThreadState_Current;
        PyObject* tmp_tb = tstate->curexc_traceback;
        if (tb != tmp_tb) {
            Py_INCREF(tb);
            tstate->curexc_traceback = tb;
            Py_XDECREF(tmp_tb);
        }
#else
        PyObject *tmp_type, *tmp_value, *tmp_tb;
        PyErr_Fetch(&tmp_type, &tmp_value, &tmp_tb);
        Py_INCREF(tb);
        PyErr_Restore(tmp_type, tmp_value, tb);
        Py_XDECREF(tmp_tb);
#endif
    }
bad:
    Py_XDECREF(owned_instance);
    return;
}
#endif

/* ExtTypeTest */
static CYTHON_INLINE int __Pyx_TypeTest(PyObject *obj, PyTypeObject *type) {
    if (unlikely(!type)) {
        PyErr_SetString(PyExc_SystemError, "Missing type object");
        return 0;
    }
    if (likely(__Pyx_TypeCheck(obj, type)))
        return 1;
    PyErr_Format(PyExc_TypeError, "Cannot convert %.200s to %.200s",
                 Py_TYPE(obj)->tp_name, type->tp_name);
    return 0;
}

/* TypeImport */
#ifndef __PYX_HAVE_RT_ImportType_0_29_36
#define __PYX_HAVE_RT_ImportType_0_29_36
static PyTypeObject *__Pyx_ImportType_0_29_36(PyObject *module, const char *module_name, const char *class_name,
    size_t size, size_t alignment, enum __Pyx_ImportType_CheckSize_0_29_36 check_size)
{
    PyObject *result = 0;
    char warning[200];
    Py_ssize_t basicsize;
    Py_ssize_t itemsize;
#ifdef Py_LIMITED_API
    PyObject *py_basicsize;
    PyObject *py_itemsize;
#endif
    result = PyObject_GetAttrString(module, class_name);
    if (!result)
        goto bad;
    if (!PyType_Check(result)) {
        PyErr_Format(PyExc_TypeError,
            "%.200s.%.200s is not a type object",
            module_name, class_name);
        goto bad;
    }
#ifndef Py_LIMITED_API
    basicsize = ((PyTypeObject *)result)->tp_basicsize;
    itemsize = ((PyTypeObject *)result)->tp_itemsize;
#else
    py_basicsize = PyObject_GetAttrString(result, "__basicsize__");
    if (!py_basicsize)
        goto bad;
    basicsize = PyLong_AsSsize_t(py_basicsize);
    Py_DECREF(py_basicsize);
    py_basicsize = 0;
    if (basicsize == (Py_ssize_t)-1 && PyErr_Occurred())
        goto bad;
    py_itemsize = PyObject_GetAttrString(result, "__itemsize__");
    if (!py_itemsize)
        goto bad;
    itemsize = PyLong_AsSsize_t(py_itemsize);
    Py_DECREF(py_itemsize);
    py_itemsize = 0;
    if (itemsize == (Py_ssize_t)-1 && PyErr_Occurred())
        goto bad;
#endif
    if (itemsize) {
        if (size % alignment) {
            alignment = size % alignment;
        }
        if (itemsize < (Py_ssize_t)alignment)
            itemsize = (Py_ssize_t)alignment;
    }
    if ((size_t)(basicsize + itemsize) < size) {
        PyErr_Format(PyExc_ValueError,
            "%.200s.%.200s size changed, may indicate binary incompatibility. "
            "Expected %zd from C header, got %zd from PyObject",
            module_name, class_name, size, basicsize);
        goto bad;
    }
    if (check_size == __Pyx_ImportType_CheckSize_Error_0_29_36 && (size_t)basicsize != size) {
        PyErr_Format(PyExc_ValueError,
            "%.200s.%.200s size changed, may indicate binary incompatibility. "
            "Expected %zd from C header, got %zd from PyObject",
            module_name, class_name, size, basicsize);
        goto bad;
    }
    else if (check_size == __Pyx_ImportType_CheckSize_Warn_0_29_36 && (size_t)basicsize > size) {
        PyOS_snprintf(warning, sizeof(warning),
            "%s.%s size changed, may indicate binary incompatibility. "
            "Expected %zd from C header, got %zd from PyObject",
            module_name, class_name, size, basicsize);
        if (PyErr_WarnEx(NULL, warning, 0) < 0) goto bad;
    }
    return (PyTypeObject *)result;
bad:
    Py_XDECREF(result);
    return NULL;
}
#endif

/* GetVTable */
static void* __Pyx_GetVtable(PyObject *dict) {
    void* ptr;
    PyObject *ob = PyObject_GetItem(dict, __pyx_n_s_pyx_vtable);
    if (!ob)
        goto bad;
#if PY_VERSION_HEX >= 0x02070000
    ptr = PyCapsule_GetPointer(ob, 0);
#else
    ptr = PyCObject_AsVoidPtr(ob);
#endif
    if (!ptr && !PyErr_Occurred())
        PyErr_SetString(PyExc_RuntimeError, "invalid vtable found for imported type");
    Py_DECREF(ob);
    return ptr;
bad:
    Py_XDECREF(ob);
    return NULL;
}

/* PyDictVersioning */
#if CYTHON_USE_DICT_VERSIONS && CYTHON_USE_TYPE_SLOTS
static CYTHON_INLINE PY_UINT64_T __Pyx_get_tp_dict_version(PyObject *obj) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    return likely(dict) ? __PYX_GET_DICT_VERSION(dict) : 0;
}
static CYTHON_INLINE PY_UINT64_T __Pyx_get_object_dict_version(PyObject *obj) {
    PyObject **dictptr = NULL;
    Py_ssize_t offset = Py_TYPE(obj)->tp_dictoffset;
    if (offset) {
#if CYTHON_COMPILING_IN_CPYTHON
        dictptr = (likely(offset > 0)) ? (PyObject **) ((char *)obj + offset) : _PyObject_GetDictPtr(obj);
#else
        dictptr = _PyObject_GetDictPtr(obj);
#endif
    }
    return (dictptr && *dictptr) ? __PYX_GET_DICT_VERSION(*dictptr) : 0;
}
static CYTHON_INLINE int __Pyx_object_dict_version_matches(PyObject* obj, PY_UINT64_T tp_dict_version, PY_UINT64_T obj_dict_version) {
    PyObject *dict = Py_TYPE(obj)->tp_dict;
    if (unlikely(!dict) || unlikely(tp_dict_version != __PYX_GET_DICT_VERSION(dict)))
        return 0;
    return obj_dict_version == __Pyx_get_object_dict_version(obj);
}
#endif

/* CLineInTraceback */
#ifndef CYTHON_CLINE_IN_TRACEBACK
static int __Pyx_CLineForTraceback(CYTHON_UNUSED PyThreadState *tstate, int c_line) {
    PyObject *use_cline;
    PyObject *ptype, *pvalue, *ptraceback;
#if CYTHON_COMPILING_IN_CPYTHON
    PyObject **cython_runtime_dict;
#endif
    if (unlikely(!__pyx_cython_runtime)) {
        return c_line;
    }
    __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
#if CYTHON_COMPILING_IN_CPYTHON
    cython_runtime_dict = _PyObject_GetDictPtr(__pyx_cython_runtime);
    if (likely(cython_runtime_dict)) {
        __PYX_PY_DICT_LOOKUP_IF_MODIFIED(
            use_cline, *cython_runtime_dict,
            __Pyx_PyDict_GetItemStr(*cython_runtime_dict, __pyx_n_s_cline_in_traceback))
    } else
#endif
    {
      PyObject *use_cline_obj = __Pyx_PyObject_GetAttrStr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback);
      if (use_cline_obj) {
        use_cline = PyObject_Not(use_cline_obj) ? Py_False : Py_True;
        Py_DECREF(use_cline_obj);
      } else {
        PyErr_Clear();
        use_cline = NULL;
      }
    }
    if (!use_cline) {
        c_line = 0;
        (void) PyObject_SetAttr(__pyx_cython_runtime, __pyx_n_s_cline_in_traceback, Py_False);
    }
    else if (use_cline == Py_False || (use_cline != Py_True && PyObject_Not(use_cline) != 0)) {
        c_line = 0;
    }
    __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
    return c_line;
}
#endif

/* CodeObjectCache */
static int __pyx_bisect_code_objects(__Pyx_CodeObjectCacheEntry* entries, int count, int code_line) {
    int start = 0, mid = 0, end = count - 1;
    if (end >= 0 && code_line > entries[end].code_line) {
        return count;
    }
    while (start < end) {
        mid = start + (end - start) / 2;
        if (code_line < entries[mid].code_line) {
            end = mid;
        } else if (code_line > entries[mid].code_line) {
             start = mid + 1;
        } else {
            return mid;
        }
    }
    if (code_line <= entries[mid].code_line) {
        return mid;
    } else {
        return mid + 1;
    }
}
static PyCodeObject *__pyx_find_code_object(int code_line) {
    PyCodeObject* code_object;
    int pos;
    if (unlikely(!code_line) || unlikely(!__pyx_code_cache.entries)) {
        return NULL;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if (unlikely(pos >= __pyx_code_cache.count) || unlikely(__pyx_code_cache.entries[pos].code_line != code_line)) {
        return NULL;
    }
    code_object = __pyx_code_cache.entries[pos].code_object;
    Py_INCREF(code_object);
    return code_object;
}
static void __pyx_insert_code_object(int code_line, PyCodeObject* code_object) {
    int pos, i;
    __Pyx_CodeObjectCacheEntry* entries = __pyx_code_cache.entries;
    if (unlikely(!code_line)) {
        return;
    }
    if (unlikely(!entries)) {
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Malloc(64*sizeof(__Pyx_CodeObjectCacheEntry));
        if (likely(entries)) {
            __pyx_code_cache.entries = entries;
            __pyx_code_cache.max_count = 64;
            __pyx_code_cache.count = 1;
            entries[0].code_line = code_line;
            entries[0].code_object = code_object;
            Py_INCREF(code_object);
        }
        return;
    }
    pos = __pyx_bisect_code_objects(__pyx_code_cache.entries, __pyx_code_cache.count, code_line);
    if ((pos < __pyx_code_cache.count) && unlikely(__pyx_code_cache.entries[pos].code_line == code_line)) {
        PyCodeObject* tmp = entries[pos].code_object;
        entries[pos].code_object = code_object;
        Py_DECREF(tmp);
        return;
    }
    if (__pyx_code_cache.count == __pyx_code_cache.max_count) {
        int new_max = __pyx_code_cache.max_count + 64;
        entries = (__Pyx_CodeObjectCacheEntry*)PyMem_Realloc(
            __pyx_code_cache.entries, ((size_t)new_max) * sizeof(__Pyx_CodeObjectCacheEntry));
        if (unlikely(!entries)) {
            return;
        }
        __pyx_code_cache.entries = entries;
        __pyx_code_cache.max_count = new_max;
    }
    for (i=__pyx_code_cache.count; i>pos; i--) {
        entries[i] = entries[i-1];
    }
    entries[pos].code_line = code_line;
    entries[pos].code_object = code_object;
    __pyx_code_cache.count++;
    Py_INCREF(code_object);
}

/* AddTraceback */
#include "compile.h"
#include "frameobject.h"
#include "traceback.h"
#if PY_VERSION_HEX >= 0x030b00a6
  #ifndef Py_BUILD_CORE
    #define Py_BUILD_CORE 1
  #endif
  #include "internal/pycore_frame.h"
#endif
static PyCodeObject* __Pyx_CreateCodeObjectForTraceback(
            const char *funcname, int c_line,
            int py_line, const char *filename) {
    PyCodeObject *py_code = NULL;
    PyObject *py_funcname = NULL;
    #if PY_MAJOR_VERSION < 3
    PyObject *py_srcfile = NULL;
    py_srcfile = PyString_FromString(filename);
    if (!py_srcfile) goto bad;
    #endif
    if (c_line) {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        #else
        py_funcname = PyUnicode_FromFormat( "%s (%s:%d)", funcname, __pyx_cfilenm, c_line);
        if (!py_funcname) goto bad;
        funcname = PyUnicode_AsUTF8(py_funcname);
        if (!funcname) goto bad;
        #endif
    }
    else {
        #if PY_MAJOR_VERSION < 3
        py_funcname = PyString_FromString(funcname);
        if (!py_funcname) goto bad;
        #endif
    }
    #if PY_MAJOR_VERSION < 3
    py_code = __Pyx_PyCode_New(
        0,
        0,
        0,
        0,
        0,
        __pyx_empty_bytes, /*PyObject *code,*/
        __pyx_empty_tuple, /*PyObject *consts,*/
        __pyx_empty_tuple, /*PyObject *names,*/
        __pyx_empty_tuple, /*PyObject *varnames,*/
        __pyx_empty_tuple, /*PyObject *freevars,*/
        __pyx_empty_tuple, /*PyObject *cellvars,*/
        py_srcfile,   /*PyObject *filename,*/
        py_funcname,  /*PyObject *name,*/
        py_line,
        __pyx_empty_bytes  /*PyObject *lnotab*/
    );
    Py_DECREF(py_srcfile);
    #else
    py_code = PyCode_NewEmpty(filename, funcname, py_line);
    #endif
    Py_XDECREF(py_funcname);  // XDECREF since it's only set on Py3 if cline
    return py_code;
bad:
    Py_XDECREF(py_funcname);
    #if PY_MAJOR_VERSION < 3
    Py_XDECREF(py_srcfile);
    #endif
    return NULL;
}
static void __Pyx_AddTraceback(const char *funcname, int c_line,
                               int py_line, const char *filename) {
    PyCodeObject *py_code = 0;
    PyFrameObject *py_frame = 0;
    PyThreadState *tstate = __Pyx_PyThreadState_Current;
    PyObject *ptype, *pvalue, *ptraceback;
    if (c_line) {
        c_line = __Pyx_CLineForTraceback(tstate, c_line);
    }
    py_code = __pyx_find_code_object(c_line ? -c_line : py_line);
    if (!py_code) {
        __Pyx_ErrFetchInState(tstate, &ptype, &pvalue, &ptraceback);
        py_code = __Pyx_CreateCodeObjectForTraceback(
            funcname, c_line, py_line, filename);
        if (!py_code) {
            /* If the code object creation fails, then we should clear the
               fetched exception references and propagate the new exception */
            Py_XDECREF(ptype);
            Py_XDECREF(pvalue);
            Py_XDECREF(ptraceback);
            goto bad;
        }
        __Pyx_ErrRestoreInState(tstate, ptype, pvalue, ptraceback);
        __pyx_insert_code_object(c_line ? -c_line : py_line, py_code);
    }
    py_frame = PyFrame_New(
        tstate,            /*PyThreadState *tstate,*/
        py_code,           /*PyCodeObject *code,*/
        __pyx_d,    /*PyObject *globals,*/
        0                  /*PyObject *locals*/
    );
    if (!py_frame) goto bad;
    __Pyx_PyFrame_SetLineNumber(py_frame, py_line);
    PyTraceBack_Here(py_frame);
bad:
    Py_XDECREF(py_code);
    Py_XDECREF(py_frame);
}

/* CIntFromPyVerify */
#define __PYX_VERIFY_RETURN_INT(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 0)
#define __PYX_VERIFY_RETURN_INT_EXC(target_type, func_type, func_value)\
    __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, 1)
#define __PYX__VERIFY_RETURN_INT(target_type, func_type, func_value, exc)\
    {\
        func_type value = func_value;\
        if (sizeof(target_type) < sizeof(func_type)) {\
            if (unlikely(value != (func_type) (target_type) value)) {\
                func_type zero = 0;\
                if (exc && unlikely(value == (func_type)-1 && PyErr_Occurred()))\
                    return (target_type) -1;\
                if (is_unsigned && unlikely(value < zero))\
                    goto raise_neg_overflow;\
                else\
                    goto raise_overflow;\
            }\
        }\
        return (target_type) value;\
    }

/* Declarations */
#if CYTHON_CCOMPLEX
  #ifdef __cplusplus
    static CYTHON_INLINE __pyx_t_float_complex __pyx_t_float_complex_from_parts(float x, float y) {
      return ::std::complex< float >(x, y);
    }
  #else
    static CYTHON_INLINE __pyx_t_float_complex __pyx_t_float_complex_from_parts(float x, float y) {
      return x + y*(__pyx_t_float_complex)_Complex_I;
    }
  #endif
#else
    static CYTHON_INLINE __pyx_t_float_complex __pyx_t_float_complex_from_parts(float x, float y) {
      __pyx_t_float_complex z;
      z.real = x;
      z.imag = y;
      return z;
    }
#endif

/* Arithmetic */
#if CYTHON_CCOMPLEX
#else
    static CYTHON_INLINE int __Pyx_c_eq_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
       return (a.real == b.real) && (a.imag == b.imag);
    }
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_sum_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
        __pyx_t_float_complex z;
        z.real = a.real + b.real;
        z.imag = a.imag + b.imag;
        return z;
    }
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_diff_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
        __pyx_t_float_complex z;
        z.real = a.real - b.real;
        z.imag = a.imag - b.imag;
        return z;
    }
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_prod_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
        __pyx_t_float_complex z;
        z.real = a.real * b.real - a.imag * b.imag;
        z.imag = a.real * b.imag + a.imag * b.real;
        return z;
    }
    #if 1
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_quot_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
        if (b.imag == 0) {
            return __pyx_t_float_complex_from_parts(a.real / b.real, a.imag / b.real);
        } else if (fabsf(b.real) >= fabsf(b.imag)) {
            if (b.real == 0 && b.imag == 0) {
                return __pyx_t_float_complex_from_parts(a.real / b.real, a.imag / b.imag);
            } else {
                float r = b.imag / b.real;
                float s = (float)(1.0) / (b.real + b.imag * r);
                return __pyx_t_float_complex_from_parts(
                    (a.real + a.imag * r) * s, (a.imag - a.real * r) * s);
            }
        } else {
            float r = b.real / b.imag;
            float s = (float)(1.0) / (b.imag + b.real * r);
            return __pyx_t_float_complex_from_parts(
                (a.real * r + a.imag) * s, (a.imag * r - a.real) * s);
        }
    }
    #else
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_quot_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
        if (b.imag == 0) {
            return __pyx_t_float_complex_from_parts(a.real / b.real, a.imag / b.real);
        } else {
            float denom = b.real * b.real + b.imag * b.imag;
            return __pyx_t_float_complex_from_parts(
                (a.real * b.real + a.imag * b.imag) / denom,
                (a.imag * b.real - a.real * b.imag) / denom);
        }
    }
    #endif
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_neg_float(__pyx_t_float_complex a) {
        __pyx_t_float_complex z;
        z.real = -a.real;
        z.imag = -a.imag;
        return z;
    }
    static CYTHON_INLINE int __Pyx_c_is_zero_float(__pyx_t_float_complex a) {
       return (a.real == 0) && (a.imag == 0);
    }
    static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_conj_float(__pyx_t_float_complex a) {
        __pyx_t_float_complex z;
        z.real =  a.real;
        z.imag = -a.imag;
        return z;
    }
    #if 1
        static CYTHON_INLINE float __Pyx_c_abs_float(__pyx_t_float_complex z) {
          #if !defined(HAVE_HYPOT) || defined(_MSC_VER)
            return sqrtf(z.real*z.real + z.imag*z.imag);
          #else
            return hypotf(z.real, z.imag);
          #endif
        }
        static CYTHON_INLINE __pyx_t_float_complex __Pyx_c_pow_float(__pyx_t_float_complex a, __pyx_t_float_complex b) {
            __pyx_t_float_complex z;
            float r, lnr, theta, z_r, z_theta;
            if (b.imag == 0 && b.real == (int)b.real) {
                if (b.real < 0) {
                    float denom = a.real * a.real + a.imag * a.imag;
                    a.real = a.real / denom;
                    a.imag = -a.imag / denom;
                    b.real = -b.real;
                }
                switch ((int)b.real) {
                    case 0:
                        z.real = 1;
                        z.imag = 0;
                        return z;
                    case 1:
                        return a;
                    case 2:
                        return __Pyx_c_prod_float(a, a);
                    case 3:
                        z = __Pyx_c_prod_float(a, a);
                        return __Pyx_c_prod_float(z, a);
                    case 4:
                        z = __Pyx_c_prod_float(a, a);
                        return __Pyx_c_prod_float(z, z);
                }
            }
            if (a.imag == 0) {
                if (a.real == 0) {
                    return a;
                } else if ((b.imag == 0) && (a.real >= 0)) {
                    z.real = powf(a.real, b.real);
                    z.imag = 0;
                    return z;
                } else if (a.real > 0) {
                    r = a.real;
                    theta = 0;
                } else {
                    r = -a.real;
                    theta = atan2f(0.0, -1.0);
                }
            } else {
                r = __Pyx_c_abs_float(a);
                theta = atan2f(a.imag, a.real);
            }
            lnr = logf(r);
            z_r = expf(lnr * b.real - theta * b.imag);
            z_theta = theta * b.real + lnr * b.imag;
            z.real = z_r * cosf(z_theta);
            z.imag = z_r * sinf(z_theta);
            return z;
        }
    #endif
#endif

/* Declarations */
#if CYTHON_CCOMPLEX
  #ifdef __cplusplus
    static CYTHON_INLINE __pyx_t_double_complex __pyx_t_double_complex_from_parts(double x, double y) {
      return ::std::complex< double >(x, y);
    }
  #else
    static CYTHON_INLINE __pyx_t_double_complex __pyx_t_double_complex_from_parts(double x, double y) {
      return x + y*(__pyx_t_double_complex)_Complex_I;
    }
  #endif
#else
    static CYTHON_INLINE __pyx_t_double_complex __pyx_t_double_complex_from_parts(double x, double y) {
      __pyx_t_double_complex z;
      z.real = x;
      z.imag = y;
      return z;
    }
#endif

/* Arithmetic */
#if CYTHON_CCOMPLEX
#else
    static CYTHON_INLINE int __Pyx_c_eq_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
       return (a.real == b.real) && (a.imag == b.imag);
    }
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_sum_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
        __pyx_t_double_complex z;
        z.real = a.real + b.real;
        z.imag = a.imag + b.imag;
        return z;
    }
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_diff_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
        __pyx_t_double_complex z;
        z.real = a.real - b.real;
        z.imag = a.imag - b.imag;
        return z;
    }
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_prod_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
        __pyx_t_double_complex z;
        z.real = a.real * b.real - a.imag * b.imag;
        z.imag = a.real * b.imag + a.imag * b.real;
        return z;
    }
    #if 1
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_quot_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
        if (b.imag == 0) {
            return __pyx_t_double_complex_from_parts(a.real / b.real, a.imag / b.real);
        } else if (fabs(b.real) >= fabs(b.imag)) {
            if (b.real == 0 && b.imag == 0) {
                return __pyx_t_double_complex_from_parts(a.real / b.real, a.imag / b.imag);
            } else {
                double r = b.imag / b.real;
                double s = (double)(1.0) / (b.real + b.imag * r);
                return __pyx_t_double_complex_from_parts(
                    (a.real + a.imag * r) * s, (a.imag - a.real * r) * s);
            }
        } else {
            double r = b.real / b.imag;
            double s = (double)(1.0) / (b.imag + b.real * r);
            return __pyx_t_double_complex_from_parts(
                (a.real * r + a.imag) * s, (a.imag * r - a.real) * s);
        }
    }
    #else
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_quot_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
        if (b.imag == 0) {
            return __pyx_t_double_complex_from_parts(a.real / b.real, a.imag / b.real);
        } else {
            double denom = b.real * b.real + b.imag * b.imag;
            return __pyx_t_double_complex_from_parts(
                (a.real * b.real + a.imag * b.imag) / denom,
                (a.imag * b.real - a.real * b.imag) / denom);
        }
    }
    #endif
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_neg_double(__pyx_t_double_complex a) {
        __pyx_t_double_complex z;
        z.real = -a.real;
        z.imag = -a.imag;
        return z;
    }
    static CYTHON_INLINE int __Pyx_c_is_zero_double(__pyx_t_double_complex a) {
       return (a.real == 0) && (a.imag == 0);
    }
    static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_conj_double(__pyx_t_double_complex a) {
        __pyx_t_double_complex z;
        z.real =  a.real;
        z.imag = -a.imag;
        return z;
    }
    #if 1
        static CYTHON_INLINE double __Pyx_c_abs_double(__pyx_t_double_complex z) {
          #if !defined(HAVE_HYPOT) || defined(_MSC_VER)
            return sqrt(z.real*z.real + z.imag*z.imag);
          #else
            return hypot(z.real, z.imag);
          #endif
        }
        static CYTHON_INLINE __pyx_t_double_complex __Pyx_c_pow_double(__pyx_t_double_complex a, __pyx_t_double_complex b) {
            __pyx_t_double_complex z;
            double r, lnr, theta, z_r, z_theta;
            if (b.imag == 0 && b.real == (int)b.real) {
                if (b.real < 0) {
                    double denom = a.real * a.real + a.imag * a.imag;
                    a.real = a.real / denom;
                    a.imag = -a.imag / denom;
                    b.real = -b.real;
                }
                switch ((int)b.real) {
                    case 0:
                        z.real = 1;
                        z.imag = 0;
                        return z;
                    case 1:
                        return a;
                    case 2:
                        return __Pyx_c_prod_double(a, a);
                    case 3:
                        z = __Pyx_c_prod_double(a, a);
                        return __Pyx_c_prod_double(z, a);
                    case 4:
                        z = __Pyx_c_prod_double(a, a);
                        return __Pyx_c_prod_double(z, z);
                }
            }
            if (a.imag == 0) {
                if (a.real == 0) {
                    return a;
                } else if ((b.imag == 0) && (a.real >= 0)) {
                    z.real = pow(a.real, b.real);
                    z.imag = 0;
                    return z;
                } else if (a.real > 0) {
                    r = a.real;
                    theta = 0;
                } else {
                    r = -a.real;
                    theta = atan2(0.0, -1.0);
                }
            } else {
                r = __Pyx_c_abs_double(a);
                theta = atan2(a.imag, a.real);
            }
            lnr = log(r);
            z_r = exp(lnr * b.real - theta * b.imag);
            z_theta = theta * b.real + lnr * b.imag;
            z.real = z_r * cos(z_theta);
            z.imag = z_r * sin(z_theta);
            return z;
        }
    #endif
#endif

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_int(int value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(int) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(int) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(int) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(int),
                                     little, !is_unsigned);
    }
}

/* CIntFromPy */
static CYTHON_INLINE int __Pyx_PyInt_As_int(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const int neg_one = (int) -1, const_zero = (int) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(int) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(int, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (int) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case  1: __PYX_VERIFY_RETURN_INT(int, digit, digits[0])
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 2 * PyLong_SHIFT) {
                            return (int) (((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 3 * PyLong_SHIFT) {
                            return (int) (((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) >= 4 * PyLong_SHIFT) {
                            return (int) (((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (int) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(int) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (int) 0;
                case -1: __PYX_VERIFY_RETURN_INT(int, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(int,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(int) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(int) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                            return (int) ((((((int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(int) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(int) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                            return (int) ((((((((int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(int) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) (((int)-1)*(((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(int) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(int, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(int) - 1 > 4 * PyLong_SHIFT) {
                            return (int) ((((((((((int)digits[3]) << PyLong_SHIFT) | (int)digits[2]) << PyLong_SHIFT) | (int)digits[1]) << PyLong_SHIFT) | (int)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(int) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(int) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(int, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            int val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (int) -1;
        }
    } else {
        int val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (int) -1;
        val = __Pyx_PyInt_As_int(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to int");
    return (int) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to int");
    return (int) -1;
}

/* CIntFromPy */
static CYTHON_INLINE size_t __Pyx_PyInt_As_size_t(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const size_t neg_one = (size_t) -1, const_zero = (size_t) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(size_t) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(size_t, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (size_t) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (size_t) 0;
                case  1: __PYX_VERIFY_RETURN_INT(size_t, digit, digits[0])
                case 2:
                    if (8 * sizeof(size_t) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) >= 2 * PyLong_SHIFT) {
                            return (size_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(size_t) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) >= 3 * PyLong_SHIFT) {
                            return (size_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(size_t) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) >= 4 * PyLong_SHIFT) {
                            return (size_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (size_t) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(size_t) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(size_t, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(size_t) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(size_t, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (size_t) 0;
                case -1: __PYX_VERIFY_RETURN_INT(size_t, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(size_t,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(size_t) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) - 1 > 2 * PyLong_SHIFT) {
                            return (size_t) (((size_t)-1)*(((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(size_t) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) - 1 > 2 * PyLong_SHIFT) {
                            return (size_t) ((((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(size_t) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) - 1 > 3 * PyLong_SHIFT) {
                            return (size_t) (((size_t)-1)*(((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(size_t) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) - 1 > 3 * PyLong_SHIFT) {
                            return (size_t) ((((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(size_t) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) - 1 > 4 * PyLong_SHIFT) {
                            return (size_t) (((size_t)-1)*(((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(size_t) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(size_t, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(size_t) - 1 > 4 * PyLong_SHIFT) {
                            return (size_t) ((((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(size_t) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(size_t, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(size_t) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(size_t, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            size_t val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (size_t) -1;
        }
    } else {
        size_t val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (size_t) -1;
        val = __Pyx_PyInt_As_size_t(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to size_t");
    return (size_t) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to size_t");
    return (size_t) -1;
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_uint64_t(uint64_t value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const uint64_t neg_one = (uint64_t) -1, const_zero = (uint64_t) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(uint64_t) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(uint64_t) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(uint64_t) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(uint64_t) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(uint64_t) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(uint64_t),
                                     little, !is_unsigned);
    }
}

/* CIntToPy */
static CYTHON_INLINE PyObject* __Pyx_PyInt_From_long(long value) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
    if (is_unsigned) {
        if (sizeof(long) < sizeof(long)) {
            return PyInt_FromLong((long) value);
        } else if (sizeof(long) <= sizeof(unsigned long)) {
            return PyLong_FromUnsignedLong((unsigned long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
            return PyLong_FromUnsignedLongLong((unsigned PY_LONG_LONG) value);
#endif
        }
    } else {
        if (sizeof(long) <= sizeof(long)) {
            return PyInt_FromLong((long) value);
#ifdef HAVE_LONG_LONG
        } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
            return PyLong_FromLongLong((PY_LONG_LONG) value);
#endif
        }
    }
    {
        int one = 1; int little = (int)*(unsigned char *)&one;
        unsigned char *bytes = (unsigned char *)&value;
        return _PyLong_FromByteArray(bytes, sizeof(long),
                                     little, !is_unsigned);
    }
}

/* CIntFromPy */
static CYTHON_INLINE long __Pyx_PyInt_As_long(PyObject *x) {
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wconversion"
#endif
    const long neg_one = (long) -1, const_zero = (long) 0;
#ifdef __Pyx_HAS_GCC_DIAGNOSTIC
#pragma GCC diagnostic pop
#endif
    const int is_unsigned = neg_one > const_zero;
#if PY_MAJOR_VERSION < 3
    if (likely(PyInt_Check(x))) {
        if (sizeof(long) < sizeof(long)) {
            __PYX_VERIFY_RETURN_INT(long, long, PyInt_AS_LONG(x))
        } else {
            long val = PyInt_AS_LONG(x);
            if (is_unsigned && unlikely(val < 0)) {
                goto raise_neg_overflow;
            }
            return (long) val;
        }
    } else
#endif
    if (likely(PyLong_Check(x))) {
        if (is_unsigned) {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case  1: __PYX_VERIFY_RETURN_INT(long, digit, digits[0])
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 2 * PyLong_SHIFT) {
                            return (long) (((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 3 * PyLong_SHIFT) {
                            return (long) (((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) >= 4 * PyLong_SHIFT) {
                            return (long) (((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0]));
                        }
                    }
                    break;
            }
#endif
#if CYTHON_COMPILING_IN_CPYTHON && PY_VERSION_HEX < 0x030C00A7
            if (unlikely(Py_SIZE(x) < 0)) {
                goto raise_neg_overflow;
            }
#else
            {
                int result = PyObject_RichCompareBool(x, Py_False, Py_LT);
                if (unlikely(result < 0))
                    return (long) -1;
                if (unlikely(result == 1))
                    goto raise_neg_overflow;
            }
#endif
            if (sizeof(long) <= sizeof(unsigned long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned long, PyLong_AsUnsignedLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(unsigned PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, unsigned PY_LONG_LONG, PyLong_AsUnsignedLongLong(x))
#endif
            }
        } else {
#if CYTHON_USE_PYLONG_INTERNALS
            const digit* digits = ((PyLongObject*)x)->ob_digit;
            switch (Py_SIZE(x)) {
                case  0: return (long) 0;
                case -1: __PYX_VERIFY_RETURN_INT(long, sdigit, (sdigit) (-(sdigit)digits[0]))
                case  1: __PYX_VERIFY_RETURN_INT(long,  digit, +digits[0])
                case -2:
                    if (8 * sizeof(long) - 1 > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 2:
                    if (8 * sizeof(long) > 1 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 2 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                            return (long) ((((((long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -3:
                    if (8 * sizeof(long) - 1 > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 3:
                    if (8 * sizeof(long) > 2 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 3 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                            return (long) ((((((((long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case -4:
                    if (8 * sizeof(long) - 1 > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, long, -(long) (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) (((long)-1)*(((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
                case 4:
                    if (8 * sizeof(long) > 3 * PyLong_SHIFT) {
                        if (8 * sizeof(unsigned long) > 4 * PyLong_SHIFT) {
                            __PYX_VERIFY_RETURN_INT(long, unsigned long, (((((((((unsigned long)digits[3]) << PyLong_SHIFT) | (unsigned long)digits[2]) << PyLong_SHIFT) | (unsigned long)digits[1]) << PyLong_SHIFT) | (unsigned long)digits[0])))
                        } else if (8 * sizeof(long) - 1 > 4 * PyLong_SHIFT) {
                            return (long) ((((((((((long)digits[3]) << PyLong_SHIFT) | (long)digits[2]) << PyLong_SHIFT) | (long)digits[1]) << PyLong_SHIFT) | (long)digits[0])));
                        }
                    }
                    break;
            }
#endif
            if (sizeof(long) <= sizeof(long)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, long, PyLong_AsLong(x))
#ifdef HAVE_LONG_LONG
            } else if (sizeof(long) <= sizeof(PY_LONG_LONG)) {
                __PYX_VERIFY_RETURN_INT_EXC(long, PY_LONG_LONG, PyLong_AsLongLong(x))
#endif
            }
        }
        {
#if CYTHON_COMPILING_IN_PYPY && !defined(_PyLong_AsByteArray)
            PyErr_SetString(PyExc_RuntimeError,
                            "_PyLong_AsByteArray() not available in PyPy, cannot convert large numbers");
#else
            long val;
            PyObject *v = __Pyx_PyNumber_IntOrLong(x);
 #if PY_MAJOR_VERSION < 3
            if (likely(v) && !PyLong_Check(v)) {
                PyObject *tmp = v;
                v = PyNumber_Long(tmp);
                Py_DECREF(tmp);
            }
 #endif
            if (likely(v)) {
                int one = 1; int is_little = (int)*(unsigned char *)&one;
                unsigned char *bytes = (unsigned char *)&val;
                int ret = _PyLong_AsByteArray((PyLongObject *)v,
                                              bytes, sizeof(val),
                                              is_little, !is_unsigned);
                Py_DECREF(v);
                if (likely(!ret))
                    return val;
            }
#endif
            return (long) -1;
        }
    } else {
        long val;
        PyObject *tmp = __Pyx_PyNumber_IntOrLong(x);
        if (!tmp) return (long) -1;
        val = __Pyx_PyInt_As_long(tmp);
        Py_DECREF(tmp);
        return val;
    }
raise_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "value too large to convert to long");
    return (long) -1;
raise_neg_overflow:
    PyErr_SetString(PyExc_OverflowError,
        "can't convert negative value to long");
    return (long) -1;
}

/* FastTypeChecks */
#if CYTHON_COMPILING_IN_CPYTHON
static int __Pyx_InBases(PyTypeObject *a, PyTypeObject *b) {
    while (a) {
        a = a->tp_base;
        if (a == b)
            return 1;
    }
    return b == &PyBaseObject_Type;
}
static CYTHON_INLINE int __Pyx_IsSubtype(PyTypeObject *a, PyTypeObject *b) {
    PyObject *mro;
    if (a == b) return 1;
    mro = a->tp_mro;
    if (likely(mro)) {
        Py_ssize_t i, n;
        n = PyTuple_GET_SIZE(mro);
        for (i = 0; i < n; i++) {
            if (PyTuple_GET_ITEM(mro, i) == (PyObject *)b)
                return 1;
        }
        return 0;
    }
    return __Pyx_InBases(a, b);
}
#if PY_MAJOR_VERSION == 2
static int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject* exc_type2) {
    PyObject *exception, *value, *tb;
    int res;
    __Pyx_PyThreadState_declare
    __Pyx_PyThreadState_assign
    __Pyx_ErrFetch(&exception, &value, &tb);
    res = exc_type1 ? PyObject_IsSubclass(err, exc_type1) : 0;
    if (unlikely(res == -1)) {
        PyErr_WriteUnraisable(err);
        res = 0;
    }
    if (!res) {
        res = PyObject_IsSubclass(err, exc_type2);
        if (unlikely(res == -1)) {
            PyErr_WriteUnraisable(err);
            res = 0;
        }
    }
    __Pyx_ErrRestore(exception, value, tb);
    return res;
}
#else
static CYTHON_INLINE int __Pyx_inner_PyErr_GivenExceptionMatches2(PyObject *err, PyObject* exc_type1, PyObject *exc_type2) {
    int res = exc_type1 ? __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type1) : 0;
    if (!res) {
        res = __Pyx_IsSubtype((PyTypeObject*)err, (PyTypeObject*)exc_type2);
    }
    return res;
}
#endif
static int __Pyx_PyErr_GivenExceptionMatchesTuple(PyObject *exc_type, PyObject *tuple) {
    Py_ssize_t i, n;
    assert(PyExceptionClass_Check(exc_type));
    n = PyTuple_GET_SIZE(tuple);
#if PY_MAJOR_VERSION >= 3
    for (i=0; i<n; i++) {
        if (exc_type == PyTuple_GET_ITEM(tuple, i)) return 1;
    }
#endif
    for (i=0; i<n; i++) {
        PyObject *t = PyTuple_GET_ITEM(tuple, i);
        #if PY_MAJOR_VERSION < 3
        if (likely(exc_type == t)) return 1;
        #endif
        if (likely(PyExceptionClass_Check(t))) {
            if (__Pyx_inner_PyErr_GivenExceptionMatches2(exc_type, NULL, t)) return 1;
        } else {
        }
    }
    return 0;
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches(PyObject *err, PyObject* exc_type) {
    if (likely(err == exc_type)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        if (likely(PyExceptionClass_Check(exc_type))) {
            return __Pyx_inner_PyErr_GivenExceptionMatches2(err, NULL, exc_type);
        } else if (likely(PyTuple_Check(exc_type))) {
            return __Pyx_PyErr_GivenExceptionMatchesTuple(err, exc_type);
        } else {
        }
    }
    return PyErr_GivenExceptionMatches(err, exc_type);
}
static CYTHON_INLINE int __Pyx_PyErr_GivenExceptionMatches2(PyObject *err, PyObject *exc_type1, PyObject *exc_type2) {
    assert(PyExceptionClass_Check(exc_type1));
    assert(PyExceptionClass_Check(exc_type2));
    if (likely(err == exc_type1 || err == exc_type2)) return 1;
    if (likely(PyExceptionClass_Check(err))) {
        return __Pyx_inner_PyErr_GivenExceptionMatches2(err, exc_type1, exc_type2);
    }
    return (PyErr_GivenExceptionMatches(err, exc_type1) || PyErr_GivenExceptionMatches(err, exc_type2));
}
#endif

/* CheckBinaryVersion */
static int __Pyx_check_binary_version(void) {
    char ctversion[5];
    int same=1, i, found_dot;
    const char* rt_from_call = Py_GetVersion();
    PyOS_snprintf(ctversion, 5, "%d.%d", PY_MAJOR_VERSION, PY_MINOR_VERSION);
    found_dot = 0;
    for (i = 0; i < 4; i++) {
        if (!ctversion[i]) {
            same = (rt_from_call[i] < '0' || rt_from_call[i] > '9');
            break;
        }
        if (rt_from_call[i] != ctversion[i]) {
            same = 0;
            break;
        }
    }
    if (!same) {
        char rtversion[5] = {'\0'};
        char message[200];
        for (i=0; i<4; ++i) {
            if (rt_from_call[i] == '.') {
                if (found_dot) break;
                found_dot = 1;
            } else if (rt_from_call[i] < '0' || rt_from_call[i] > '9') {
                break;
            }
            rtversion[i] = rt_from_call[i];
        }
        PyOS_snprintf(message, sizeof(message),
                      "compiletime version %s of module '%.100s' "
                      "does not match runtime version %s",
                      ctversion, __Pyx_MODULE_NAME, rtversion);
        return PyErr_WarnEx(NULL, message, 1);
    }
    return 0;
}

/* VoidPtrImport */
#ifndef __PYX_HAVE_RT_ImportVoidPtr_0_29_36
#define __PYX_HAVE_RT_ImportVoidPtr_0_29_36
static int __Pyx_ImportVoidPtr_0_29_36(PyObject *module, const char *name, void **p, const char *sig) {
    PyObject *d = 0;
    PyObject *cobj = 0;
    d = PyObject_GetAttrString(module, (char *)"__pyx_capi__");
    if (!d)
        goto bad;
    cobj = PyDict_GetItemString(d, name);
    if (!cobj) {
        PyErr_Format(PyExc_ImportError,
            "%.200s does not export expected C variable %.200s",
                PyModule_GetName(module), name);
        goto bad;
    }
#if PY_VERSION_HEX >= 0x02070000
    if (!PyCapsule_IsValid(cobj, sig)) {
        PyErr_Format(PyExc_TypeError,
            "C variable %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), name, sig, PyCapsule_GetName(cobj));
        goto bad;
    }
    *p = PyCapsule_GetPointer(cobj, sig);
#else
    {const char *desc, *s1, *s2;
    desc = (const char *)PyCObject_GetDesc(cobj);
    if (!desc)
        goto bad;
    s1 = desc; s2 = sig;
    while (*s1 != '\0' && *s1 == *s2) { s1++; s2++; }
    if (*s1 != *s2) {
        PyErr_Format(PyExc_TypeError,
            "C variable %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), name, sig, desc);
        goto bad;
    }
    *p = PyCObject_AsVoidPtr(cobj);}
#endif
    if (!(*p))
        goto bad;
    Py_DECREF(d);
    return 0;
bad:
    Py_XDECREF(d);
    return -1;
}
#endif

/* FunctionImport */
#ifndef __PYX_HAVE_RT_ImportFunction_0_29_36
#define __PYX_HAVE_RT_ImportFunction_0_29_36
static int __Pyx_ImportFunction_0_29_36(PyObject *module, const char *funcname, void (**f)(void), const char *sig) {
    PyObject *d = 0;
    PyObject *cobj = 0;
    union {
        void (*fp)(void);
        void *p;
    } tmp;
    d = PyObject_GetAttrString(module, (char *)"__pyx_capi__");
    if (!d)
        goto bad;
    cobj = PyDict_GetItemString(d, funcname);
    if (!cobj) {
        PyErr_Format(PyExc_ImportError,
            "%.200s does not export expected C function %.200s",
                PyModule_GetName(module), funcname);
        goto bad;
    }
#if PY_VERSION_HEX >= 0x02070000
    if (!PyCapsule_IsValid(cobj, sig)) {
        PyErr_Format(PyExc_TypeError,
            "C function %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), funcname, sig, PyCapsule_GetName(cobj));
        goto bad;
    }
    tmp.p = PyCapsule_GetPointer(cobj, sig);
#else
    {const char *desc, *s1, *s2;
    desc = (const char *)PyCObject_GetDesc(cobj);
    if (!desc)
        goto bad;
    s1 = desc; s2 = sig;
    while (*s1 != '\0' && *s1 == *s2) { s1++; s2++; }
    if (*s1 != *s2) {
        PyErr_Format(PyExc_TypeError,
            "C function %.200s.%.200s has wrong signature (expected %.500s, got %.500s)",
             PyModule_GetName(module), funcname, sig, desc);
        goto bad;
    }
    tmp.p = PyCObject_AsVoidPtr(cobj);}
#endif
    *f = tmp.fp;
    if (!(*f))
        goto bad;
    Py_DECREF(d);
    return 0;
bad:
    Py_XDECREF(d);
    return -1;
}
#endif

/* InitStrings */
static int __Pyx_InitStrings(__Pyx_StringTabEntry *t) {
    while (t->p) {
        #if PY_MAJOR_VERSION < 3
        if (t->is_unicode) {
            *t->p = PyUnicode_DecodeUTF8(t->s, t->n - 1, NULL);
        } else if (t->intern) {
            *t->p = PyString_InternFromString(t->s);
        } else {
            *t->p = PyString_FromStringAndSize(t->s, t->n - 1);
        }
        #else
        if (t->is_unicode | t->is_str) {
            if (t->intern) {
                *t->p = PyUnicode_InternFromString(t->s);
            } else if (t->encoding) {
                *t->p = PyUnicode_Decode(t->s, t->n - 1, t->encoding, NULL);
            } else {
                *t->p = PyUnicode_FromStringAndSize(t->s, t->n - 1);
            }
        } else {
            *t->p = PyBytes_FromStringAndSize(t->s, t->n - 1);
        }
        #endif
        if (!*t->p)
            return -1;
        if (PyObject_Hash(*t->p) == -1)
            return -1;
        ++t;
    }
    return 0;
}

static CYTHON_INLINE PyObject* __Pyx_PyUnicode_FromString(const char* c_str) {
    return __Pyx_PyUnicode_FromStringAndSize(c_str, (Py_ssize_t)strlen(c_str));
}
static CYTHON_INLINE const char* __Pyx_PyObject_AsString(PyObject* o) {
    Py_ssize_t ignore;
    return __Pyx_PyObject_AsStringAndSize(o, &ignore);
}
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
#if !CYTHON_PEP393_ENABLED
static const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    char* defenc_c;
    PyObject* defenc = _PyUnicode_AsDefaultEncodedString(o, NULL);
    if (!defenc) return NULL;
    defenc_c = PyBytes_AS_STRING(defenc);
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    {
        char* end = defenc_c + PyBytes_GET_SIZE(defenc);
        char* c;
        for (c = defenc_c; c < end; c++) {
            if ((unsigned char) (*c) >= 128) {
                PyUnicode_AsASCIIString(o);
                return NULL;
            }
        }
    }
#endif
    *length = PyBytes_GET_SIZE(defenc);
    return defenc_c;
}
#else
static CYTHON_INLINE const char* __Pyx_PyUnicode_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
    if (unlikely(__Pyx_PyUnicode_READY(o) == -1)) return NULL;
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
    if (likely(PyUnicode_IS_ASCII(o))) {
        *length = PyUnicode_GET_LENGTH(o);
        return PyUnicode_AsUTF8(o);
    } else {
        PyUnicode_AsASCIIString(o);
        return NULL;
    }
#else
    return PyUnicode_AsUTF8AndSize(o, length);
#endif
}
#endif
#endif
static CYTHON_INLINE const char* __Pyx_PyObject_AsStringAndSize(PyObject* o, Py_ssize_t *length) {
#if __PYX_DEFAULT_STRING_ENCODING_IS_ASCII || __PYX_DEFAULT_STRING_ENCODING_IS_DEFAULT
    if (
#if PY_MAJOR_VERSION < 3 && __PYX_DEFAULT_STRING_ENCODING_IS_ASCII
            __Pyx_sys_getdefaultencoding_not_ascii &&
#endif
            PyUnicode_Check(o)) {
        return __Pyx_PyUnicode_AsStringAndSize(o, length);
    } else
#endif
#if (!CYTHON_COMPILING_IN_PYPY) || (defined(PyByteArray_AS_STRING) && defined(PyByteArray_GET_SIZE))
    if (PyByteArray_Check(o)) {
        *length = PyByteArray_GET_SIZE(o);
        return PyByteArray_AS_STRING(o);
    } else
#endif
    {
        char* result;
        int r = PyBytes_AsStringAndSize(o, &result, length);
        if (unlikely(r < 0)) {
            return NULL;
        } else {
            return result;
        }
    }
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrue(PyObject* x) {
   int is_true = x == Py_True;
   if (is_true | (x == Py_False) | (x == Py_None)) return is_true;
   else return PyObject_IsTrue(x);
}
static CYTHON_INLINE int __Pyx_PyObject_IsTrueAndDecref(PyObject* x) {
    int retval;
    if (unlikely(!x)) return -1;
    retval = __Pyx_PyObject_IsTrue(x);
    Py_DECREF(x);
    return retval;
}
static PyObject* __Pyx_PyNumber_IntOrLongWrongResultType(PyObject* result, const char* type_name) {
#if PY_MAJOR_VERSION >= 3
    if (PyLong_Check(result)) {
        if (PyErr_WarnFormat(PyExc_DeprecationWarning, 1,
                "__int__ returned non-int (type %.200s).  "
                "The ability to return an instance of a strict subclass of int "
                "is deprecated, and may be removed in a future version of Python.",
                Py_TYPE(result)->tp_name)) {
            Py_DECREF(result);
            return NULL;
        }
        return result;
    }
#endif
    PyErr_Format(PyExc_TypeError,
                 "__%.4s__ returned non-%.4s (type %.200s)",
                 type_name, type_name, Py_TYPE(result)->tp_name);
    Py_DECREF(result);
    return NULL;
}
static CYTHON_INLINE PyObject* __Pyx_PyNumber_IntOrLong(PyObject* x) {
#if CYTHON_USE_TYPE_SLOTS
  PyNumberMethods *m;
#endif
  const char *name = NULL;
  PyObject *res = NULL;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_Check(x) || PyLong_Check(x)))
#else
  if (likely(PyLong_Check(x)))
#endif
    return __Pyx_NewRef(x);
#if CYTHON_USE_TYPE_SLOTS
  m = Py_TYPE(x)->tp_as_number;
  #if PY_MAJOR_VERSION < 3
  if (m && m->nb_int) {
    name = "int";
    res = m->nb_int(x);
  }
  else if (m && m->nb_long) {
    name = "long";
    res = m->nb_long(x);
  }
  #else
  if (likely(m && m->nb_int)) {
    name = "int";
    res = m->nb_int(x);
  }
  #endif
#else
  if (!PyBytes_CheckExact(x) && !PyUnicode_CheckExact(x)) {
    res = PyNumber_Int(x);
  }
#endif
  if (likely(res)) {
#if PY_MAJOR_VERSION < 3
    if (unlikely(!PyInt_Check(res) && !PyLong_Check(res))) {
#else
    if (unlikely(!PyLong_CheckExact(res))) {
#endif
        return __Pyx_PyNumber_IntOrLongWrongResultType(res, name);
    }
  }
  else if (!PyErr_Occurred()) {
    PyErr_SetString(PyExc_TypeError,
                    "an integer is required");
  }
  return res;
}
static CYTHON_INLINE Py_ssize_t __Pyx_PyIndex_AsSsize_t(PyObject* b) {
  Py_ssize_t ival;
  PyObject *x;
#if PY_MAJOR_VERSION < 3
  if (likely(PyInt_CheckExact(b))) {
    if (sizeof(Py_ssize_t) >= sizeof(long))
        return PyInt_AS_LONG(b);
    else
        return PyInt_AsSsize_t(b);
  }
#endif
  if (likely(PyLong_CheckExact(b))) {
    #if CYTHON_USE_PYLONG_INTERNALS
    const digit* digits = ((PyLongObject*)b)->ob_digit;
    const Py_ssize_t size = Py_SIZE(b);
    if (likely(__Pyx_sst_abs(size) <= 1)) {
        ival = likely(size) ? digits[0] : 0;
        if (size == -1) ival = -ival;
        return ival;
    } else {
      switch (size) {
         case 2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -2:
           if (8 * sizeof(Py_ssize_t) > 2 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -3:
           if (8 * sizeof(Py_ssize_t) > 3 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case 4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return (Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
         case -4:
           if (8 * sizeof(Py_ssize_t) > 4 * PyLong_SHIFT) {
             return -(Py_ssize_t) (((((((((size_t)digits[3]) << PyLong_SHIFT) | (size_t)digits[2]) << PyLong_SHIFT) | (size_t)digits[1]) << PyLong_SHIFT) | (size_t)digits[0]));
           }
           break;
      }
    }
    #endif
    return PyLong_AsSsize_t(b);
  }
  x = PyNumber_Index(b);
  if (!x) return -1;
  ival = PyInt_AsSsize_t(x);
  Py_DECREF(x);
  return ival;
}
static CYTHON_INLINE Py_hash_t __Pyx_PyIndex_AsHash_t(PyObject* o) {
  if (sizeof(Py_hash_t) == sizeof(Py_ssize_t)) {
    return (Py_hash_t) __Pyx_PyIndex_AsSsize_t(o);
#if PY_MAJOR_VERSION < 3
  } else if (likely(PyInt_CheckExact(o))) {
    return PyInt_AS_LONG(o);
#endif
  } else {
    Py_ssize_t ival;
    PyObject *x;
    x = PyNumber_Index(o);
    if (!x) return -1;
    ival = PyInt_AsLong(x);
    Py_DECREF(x);
    return ival;
  }
}
static CYTHON_INLINE PyObject * __Pyx_PyBool_FromLong(long b) {
  return b ? __Pyx_NewRef(Py_True) : __Pyx_NewRef(Py_False);
}
static CYTHON_INLINE PyObject * __Pyx_PyInt_FromSize_t(size_t ival) {
    return PyInt_FromSize_t(ival);
}


#endif /* Py_PYTHON_H */
