# import nltk
# nltk.download('words')
# from nltk.corpus import words
# import json
# import re
# import logging

# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', filename='corpusCreator.log')

# # Download NLTK corpus if not already
# nltk.download('words')
# nltk_words = set(w.lower() for w in words.words())

# try:
#     # Load JSON file
#     logging.info("Loading JSON file...")
#     with open("becn_data.json", "r", encoding="utf-8") as f:
#         json_data = json.load(f)

#     # Extract the actual data array from the "data" key
#     data = json_data["data"]
#     logging.info(f"Successfully loaded {len(data)} items")

#     # Extract relevant text fields
#     corpus_text = ""
#     processed_items = 0

#     for item in data:
#         if isinstance(item, dict):  # Safety check
#             fields = [
#                 item.get("Brand", ""),
#                 item.get("ProductTitle", ""),
#                 item.get("ProductDescription", ""),
#                 " ".join(item.get("ProductAttributes", [])) if item.get("ProductAttributes") else "",
#                 item.get("Dimensions", ""),
#                 item.get("Thickness", "")
#             ]
#             corpus_text += " ".join(fields) + " "
#             processed_items += 1

#             # Progress indicator for large files
#             if processed_items % 1000 == 0:
#                 logging.info(f"Processed {processed_items} items...")

#     logging.info(f"Processed {processed_items} items total")


#     logging.info("Removing URLs...")
#     # Remove HTTP/HTTPS URLs
#     corpus_text = re.sub(r'https?://[^\s]+', '', corpus_text, flags=re.IGNORECASE)
#     # Extract words with 3 or more letters
#     logging.info("Extracting words...")
#     bcan_words = set(re.findall(r'\b[a-zA-Z]{3,}\b', corpus_text.lower()))

#     # Find words in BCAN but not in NLTK
#     logging.info("Finding missing words...")
#     missing_words = bcan_words - nltk_words

#     # Save missing words
#     with open("bcan_missing_words.txt", "w", encoding="utf-8") as f:
#         for word in sorted(missing_words):
#             f.write(word + "\n")

#     logging.info(f"{len(missing_words)} unique BCAN-specific words written to bcan_missing_words.txt")
#     logging.info(f"Total unique words found: {len(bcan_words)}")

#     custom_word_set = nltk_words.union(missing_words)

#     # Optional: Save the combined set to a text file
#     with open("custom_corpus.txt", "w", encoding="utf-8") as f:
#         for word in sorted(custom_word_set):
#             f.write(word + "\n")

#     logging.info(f"Custom corpus created with {len(custom_word_set)} words.")

# except FileNotFoundError:
#     logging.error("Error: becn_data.json file not found")
# except json.JSONDecodeError as e:
#     logging.error(f"Error parsing JSON: {e}")
# except KeyError as e:
#     logging.error(f"Error: Key {e} not found in JSON structure")
#     logging.warning("Available keys: %s", list(json_data.keys()) if 'json_data' in locals() else "Cannot determine")
# except Exception as e:
#     logging.error(f"Unexpected error: {e}")


import nltk
nltk.download('words')
from nltk.corpus import words
import json
import re
import logging
import pickle
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', filename='corpusCreator.log')

# Path to pickle file
pickle_path = "custom_corpus.pkl"

# Try loading from pickle first
if os.path.exists(pickle_path):
    with open(pickle_path, "rb") as f:
        custom_word_set = pickle.load(f)
    logging.info(f"Loaded custom corpus from pickle with {len(custom_word_set)} words.")
else:
    # Download NLTK corpus if not already
    nltk.download('words')
    nltk_words = set(w.lower() for w in words.words())

    try:
        # Load JSON file
        logging.info("Loading JSON file...")
        with open("becn_data.json", "r", encoding="utf-8") as f:
            json_data = json.load(f)

        # Extract the actual data array from the "data" key
        data = json_data["data"]
        logging.info(f"Successfully loaded {len(data)} items")

        # Extract relevant text fields
        corpus_text = ""
        processed_items = 0

        for item in data:
            if isinstance(item, dict):  # Safety check
                fields = [
                    item.get("Brand", ""),
                    item.get("ProductTitle", ""),
                    item.get("ProductDescription", ""),
                    " ".join(item.get("ProductAttributes", [])) if item.get("ProductAttributes") else "",
                    item.get("Dimensions", ""),
                    item.get("Thickness", "")
                ]
                corpus_text += " ".join(fields) + " "
                processed_items += 1

                # Progress indicator for large files
                if processed_items % 1000 == 0:
                    logging.info(f"Processed {processed_items} items...")

        logging.info(f"Processed {processed_items} items total")

        # Clean and tokenize
        logging.info("Removing URLs...")
        corpus_text = re.sub(r'https?://[^\s]+', '', corpus_text, flags=re.IGNORECASE)

        logging.info("Extracting words...")
        bcan_words = set(re.findall(r'\b[a-zA-Z]{3,}\b', corpus_text.lower()))

        # Find missing words
        logging.info("Finding missing words...")
        missing_words = bcan_words - nltk_words

        # Save missing words
        with open("bcan_missing_words.txt", "w", encoding="utf-8") as f:
            for word in sorted(missing_words):
                f.write(word + "\n")

        logging.info(f"{len(missing_words)} unique BCAN-specific words written to bcan_missing_words.txt")
        logging.info(f"Total unique words found: {len(bcan_words)}")

        # Merge into final corpus
        custom_word_set = nltk_words.union(missing_words)

        with open("custom_corpus.txt", "w", encoding="utf-8") as f:
            for word in sorted(custom_word_set):
                f.write(word + "\n")

        logging.info(f"Custom corpus created with {len(custom_word_set)} words.")

        # ✅ Save as pickle
        with open(pickle_path, "wb") as f:
            pickle.dump(custom_word_set, f)

        logging.info(f"Custom corpus saved to {pickle_path}")

    except FileNotFoundError:
        logging.error("Error: becn_data.json file not found")
    except json.JSONDecodeError as e:
        logging.error(f"Error parsing JSON: {e}")
    except KeyError as e:
        logging.error(f"Error: Key {e} not found in JSON structure")
        logging.warning("Available keys: %s", list(json_data.keys()) if 'json_data' in locals() else "Cannot determine")
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
