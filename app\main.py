import argparse
from app.ner_utils import load_model, extract_entities
from app.spell_corrector import correct_text, load_corpus

nlp = load_model()

def main(use_spellcheck=False):
    load_corpus("custom_corpus.pkl")  # Load once at start

    with open("test_samples/examples.txt") as f:
        samples = f.readlines()

    for i, text in enumerate(samples):
        text = text.strip()
        corrected_text = correct_text(text) if use_spellcheck else text
        ents = extract_entities(corrected_text, nlp)

        print(f"\nSample #{i+1}")
        print("Original: ", text)
        if use_spellcheck:
            print("Corrected:", corrected_text)
        print("Entities:", ents)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--spellcheck", action="store_true", help="Run with spellcheck")
    args = parser.parse_args()

    main(use_spellcheck=args.spellcheck)
